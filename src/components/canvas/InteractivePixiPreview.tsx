import React from 'react';
import PixiPreviewWrapper from '../mockups/PixiPreviewWrapper';
import { useGameStore } from '../../store';

interface InteractivePixiPreviewProps {
  className?: string;
  onClose?: () => void;
}

/**
 * InteractivePixiPreview - A PixiJS preview specifically designed for the InteractiveGameCanvas
 *
 * This component uses PixiPreviewWrapper which provides the proper HTML UI overlay controls.
 * The PixiJS canvas should have showControls=false to prevent duplicate UI buttons.
 */
const InteractivePixiPreview: React.FC<InteractivePixiPreviewProps> = ({
  className = '',
  onClose
}) => {
  const { config } = useGameStore();

  return (
    <div className={`interactive-pixi-preview relative w-full h-full ${className}`}>
      {/* Use PixiPreviewWrapper which includes proper HTML UI overlay */}
      <PixiPreviewWrapper
        className="w-full h-full"
        stepSource="interactive-canvas"
      />

      {/* Optional overlay for interactive canvas specific info */}
      <div className="absolute top-4 left-4 bg-black/70 text-white rounded-lg px-3 py-2 text-sm z-50">
        <div className="font-medium">Game Preview</div>
        <div className="text-xs opacity-75">
          {config?.reels?.layout?.reels || 5}×{config?.reels?.layout?.rows || 3} Grid
        </div>
      </div>

      {/* Exit button */}
      {onClose && (
        <button
          onClick={onClose}
          className="absolute top-4 right-4 bg-black/70 hover:bg-black/90 text-white rounded-full p-2 z-50 transition-colors"
          title="Exit Preview"
        >
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
          </svg>
        </button>
      )}
    </div>
  );
};

export default InteractivePixiPreview;
