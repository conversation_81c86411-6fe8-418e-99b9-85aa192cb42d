import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import * as PIXI from 'pixi.js';
import { GlowFilter } from '@pixi/filter-glow';
import { useGameStore } from '../../store';
import { detectDeviceType } from '../../utils/deviceDetection';
import { gsap } from 'gsap';
import {
  calculatePixiMockupDimensions,
  generatePixiPlaceholderSymbols,
  createSymbolCellTexture,
  loadTextureFromUrl,
  pixiMockupStyles
} from './pixiMockupUtils';

interface PixiSlotMockupProps {
  cols: number;
  rows: number;
  symbols?: string[];
  background?: string;
  frame?: string;
  className?: string;
  showControls?: boolean;
  isMobile?: boolean;
  orientation?: 'portrait' | 'landscape';
  customOffset?: { x: number; y: number };
  hideSymbols?: boolean; // New prop to hide symbols during animation
  // Grid adjustment props
  gridAdjustments?: {
    position?: { x: number; y: number };
    scale?: number;
    stretch?: { x: number; y: number };
  };
  // Frame adjustment props
  frameAdjustments?: {
    position?: { x: number; y: number };
    scale?: number;
    stretch?: { x: number; y: number };
  };
  // Background adjustment props
  backgroundAdjustments?: {
    position?: { x: number; y: number };
    scale?: number;
    fit?: 'cover' | 'contain' | 'fill' | 'scale-down';
  };
  // UI button adjustment props
  uiButtonAdjustments?: {
    position?: { x: number; y: number };
    scale?: number;
    visibility?: boolean;
  };
  // Logo props
  logo?: string;
  logoPosition?: { x: number; y: number };
  logoScale?: number;
  logoPositioningMode?: boolean;
  onLogoPositionChange?: (position: { x: number; y: number }) => void;
  onLogoScaleChange?: (scale: number) => void;
  // Game functionality props
  onSpin?: () => void;
  onWin?: (amount: number, winType: string) => void;
  onBalanceChange?: (newBalance: number) => void;
  onSpinComplete?: () => void; // Called when animation completes
  initialBalance?: number;
  initialBet?: number;
  // Animation control props
  enableSpinAnimation?: boolean;
  spinTriggerEvent?: string; // Event name to listen for external spin triggers
  // Game state props (for external control)
  isSpinning?: boolean;
  balance?: number;
  bet?: number;
}

/**
 * PixiJS Slot Mockup Component
 *
 * Replicates the exact visual appearance of the CSS Slot Mockup
 * using PixiJS for better performance and consistency.
 *
 * PREMIUM ANIMATION IMPROVEMENTS:
 * - Issue #2 Fixed: Optimized texture pooling for smooth symbol wrapping
 * - Issue #3 Fixed: Premium easing curves with bounce effects for realistic feel
 * - Enhanced performance with reduced memory allocation during spins
 * - Smooth acceleration, constant speed, and deceleration phases
 * - Special anticipation effects for big wins and bonus features
 */
const PixiSlotMockup: React.FC<PixiSlotMockupProps> = ({
  cols,
  rows,
  symbols = [],
  background,
  frame,
  className = '',
  showControls = true,
  isMobile = false,
  orientation = 'portrait',
  customOffset = { x: 0, y: 0 },
  hideSymbols = false,
  gridAdjustments = { position: { x: 0, y: 0 }, scale: 100, stretch: { x: 100, y: 100 } },
  frameAdjustments = { position: { x: 0, y: 0 }, scale: 100, stretch: { x: 100, y: 100 } },
  backgroundAdjustments = { position: { x: 0, y: 0 }, scale: 100, fit: 'cover' },
  uiButtonAdjustments = { position: { x: 0, y: 0 }, scale: 100, visibility: true },
  logo,
  logoPosition = { x: 0, y: -50 },
  logoScale = 100,
  logoPositioningMode = false,
  onLogoPositionChange,
  onLogoScaleChange,
  // Game functionality props
  onSpin,
  onWin,
  onBalanceChange,
  onSpinComplete,
  initialBalance = 1000,
  initialBet = 1,
  // Animation control props
  enableSpinAnimation = false,
  spinTriggerEvent = 'pixiSlotSpin',
  // Game state props (for external control)
  isSpinning: externalIsSpinning,
  balance: externalBalance,
  bet: externalBet
}) => {
  const { config } = useGameStore();
  const containerRef = useRef<HTMLDivElement>(null);
  const appRef = useRef<PIXI.Application | null>(null);
  const [isReady, setIsReady] = useState(false);

  // Animation state management
  const [internalIsSpinning, setInternalIsSpinning] = useState(false);
  const [internalBalance, setInternalBalance] = useState(initialBalance);
  const [internalBet, setInternalBet] = useState(initialBet);
  const [finalSpinSymbols, setFinalSpinSymbols] = useState<string[][]>([]);
  const reelContainersRef = useRef<PIXI.Container[]>([]);
  const animationTimelineRef = useRef<gsap.core.Timeline | null>(null);
  const currentWinResultRef = useRef<{ winType: string; winMultiplier: number; winPositions: { reel: number; row: number }[] } | null>(null);
  const currentSpinResultRef = useRef<string[][] | null>(null);
  const reelSymbolsSetRef = useRef<boolean[]>([]); // Track which reels have had their final symbols set

  // Animation settings state for Step7 Animation Studio
  const [animationSettings, setAnimationSettings] = useState({
    speed: 1.0,
    blurIntensity: 8,
    easing: 'back.out',
    visualEffects: {
      spinBlur: true,
      glowEffects: false,
      screenShake: false
    }
  });

  // ISSUE #2 FIX: Texture pooling system for smooth symbol wrapping
  const texturePoolRef = useRef<PIXI.Texture[]>([]);
  const spritePoolRef = useRef<PIXI.Sprite[]>([]);

  // ISSUE #3 FIX: Premium slot machine easing curves
  const PREMIUM_EASING = useMemo(() => ({
    acceleration: "power2.out", // Smooth start with quick ramp-up
    deceleration: "back.out(1.7)", // Smooth stop with slight bounce
    constantSpeed: "none", // Linear for constant phase
    anticipation: "back.out(2.5)", // Extra bounce for big wins
    bounce: "elastic.out(1, 0.3)" // Final settle bounce
  }), []);

  // Mask controls state for Step7 Animation Studio
  const [maskControls, setMaskControls] = useState({
    enabled: true,
    debugVisible: false,
    perReelEnabled: [true, true, true, true, true]
  });

  // Store mask graphics separately for proper cleanup
  const reelMasksRef = useRef<(PIXI.Graphics | null)[]>([]);

  // Use external state if provided, otherwise use internal state
  const isSpinning = externalIsSpinning !== undefined ? externalIsSpinning : internalIsSpinning;
  const balance = externalBalance !== undefined ? externalBalance : internalBalance;
  const bet = externalBet !== undefined ? externalBet : internalBet;

  // Enhanced spin functionality with realistic slot machine behavior
  const handleSpin = useCallback(() => {
    console.log('[PixiSlotMockup] handleSpin called:', {
      isSpinning,
      balance,
      bet,
      hasApp: !!appRef.current,
      reelsCount: reelContainersRef.current.length,
      enableSpinAnimation,
      externalIsSpinning,
      internalIsSpinning
    });

    // Only block on internal spinning state, not external (to avoid circular blocking)
    const shouldBlock = (externalIsSpinning === undefined ? internalIsSpinning : false) ||
                       balance < bet ||
                       !appRef.current ||
                       !isReady ||
                       reelContainersRef.current.length === 0;

    if (shouldBlock) {
      console.log('[PixiSlotMockup] Spin blocked:', {
        internalIsSpinning,
        externalIsSpinning,
        balance,
        bet,
        hasApp: !!appRef.current,
        isReady,
        reelsCount: reelContainersRef.current.length
      });
      return;
    }

    console.log('[PixiSlotMockup] Starting enhanced spin animation');

    // Reset reel symbol flags for new spin
    reelSymbolsSetRef.current = new Array(cols).fill(false);

    // Update spinning state
    if (externalIsSpinning === undefined) {
      setInternalIsSpinning(true);
    }

    // Deduct bet from balance
    if (externalBalance === undefined) {
      setInternalBalance(prev => prev - bet);
    }

    // Call external spin handler if provided
    if (onSpin) {
      onSpin();
    }

    // Generate random symbols for the spin result (like GameEngine)
    const symbolsArray = Array.isArray(finalSymbols) ? finalSymbols :
                        Object.values(finalSymbols).filter(Boolean);

    // Complete list of all possible symbols that can be generated (matching actual naming conventions)
    const allPossibleSymbols = [
      'wild', 'wild_2',           // Wild symbols (underscore format)
      'scatter',                  // Scatter symbol
      'high_1', 'high_2', 'high_3', 'high_4',  // High value symbols (underscore format)
      'medium_1', 'medium_2', 'medium_3', 'medium_4',  // Medium value symbols (underscore format)
      'low_1', 'low_2', 'low_3', 'low_4'  // Low value symbols (underscore format)
    ];

    // Use actual user symbols if available, otherwise fallback to complete symbol set
    const availableSymbols = symbolsArray.length > 0 ? symbolsArray : allPossibleSymbols;

    // Create random grid result
    const spinResult: string[][] = [];
    for (let reel = 0; reel < cols; reel++) {
      const reelSymbols: string[] = [];
      for (let row = 0; row < rows; row++) {
        // Add some bias for wins (20% chance to create winning patterns)
        if (row === Math.floor(rows / 2) && reel < 3 && Math.random() < 0.2) {
          // Create potential winning symbol on middle row
          reelSymbols.push(availableSymbols[Math.floor(Math.random() * Math.min(3, availableSymbols.length))]);
        } else {
          reelSymbols.push(availableSymbols[Math.floor(Math.random() * availableSymbols.length)]);
        }
      }
      spinResult.push(reelSymbols);
    }

    // Use the new win detection system
    const winResult = detectWins(spinResult);

    // Store win result and spin result for highlighting and final symbol setting
    currentWinResultRef.current = winResult;
    currentSpinResultRef.current = spinResult;

    console.log('[PixiSlotMockup] Spin result:', {
      winType: winResult.winType,
      winMultiplier: winResult.winMultiplier,
      hasWin: winResult.hasWin,
      winPositions: winResult.winPositions.length
    });

    // Start the enhanced reel animation with detected win
    animateReelsEnhanced(winResult.winType, winResult.winMultiplier, spinResult);
  }, [isSpinning, balance, bet, onSpin, externalIsSpinning, externalBalance]);

  // Enhanced reel animation with realistic slot machine physics
  const animateReelsEnhanced = useCallback((winType: string, winMultiplier: number, spinResult?: string[][]) => {
    // Convert finalSymbols to array format for consistent handling
    const symbolsArray = Array.isArray(finalSymbols) ? finalSymbols :
                        Object.values(finalSymbols).filter(Boolean);
    const displaySymbols = symbolsArray.length > 0 ? symbolsArray : generatePixiPlaceholderSymbols(cols, rows);
    const isWin = winMultiplier > 0 || winType === 'freespins';

    // Clear any existing animation
    if (animationTimelineRef.current) {
      animationTimelineRef.current.kill();
    }

    // Make sure all symbol sprites are visible for animation
    reelContainersRef.current.forEach(reel => {
      reel.children.forEach(child => {
        if (child instanceof PIXI.Sprite && (child as any).isSymbolSprite === true) {
          child.visible = true;
        }
      });
    });

    // CRITICAL: Store final symbols for animation to use
    const finalSymbolsForAnimation = spinResult || null;
    currentSpinResultRef.current = finalSymbolsForAnimation;
    currentWinResultRef.current = { winType, winMultiplier, winPositions: [] };

    console.log('[PixiSlotMockup] Animation will stop at these final symbols:', finalSymbolsForAnimation);
    console.log('[PixiSlotMockup] Using animation settings:', animationSettings);

    // Create master timeline
    const masterTimeline = gsap.timeline({
      onComplete: () => {
        console.log('[PixiSlotMockup] Master timeline complete - all animations and symbol updates finished');

        // Update spinning state (animation is truly complete now)
        if (externalIsSpinning === undefined) {
          setInternalIsSpinning(false);
        }

        // Only update internal balance if not externally managed
        if (winMultiplier > 0 && externalBalance === undefined) {
          setInternalBalance(prev => prev + winMultiplier);
        }

        // Call win callback (PixiPreviewWrapper will handle balance updates)
        if (isWin && onWin) {
          onWin(winMultiplier, winType);
        }

        // Call balance change callback (informational only when externally managed)
        if (onBalanceChange && externalBalance === undefined) {
          onBalanceChange(internalBalance + winMultiplier);
        }

        // Call spin complete callback to notify parent
        if (onSpinComplete) {
          onSpinComplete();
        }

        // Emit final symbols event for external components
        window.dispatchEvent(new CustomEvent('finalSpinSymbols', {
          detail: { finalSymbols: finalSpinSymbols.flat() }
        }));

        // Trigger win highlighting after spin completes (like UnifiedSlotPreview)
        if (isWin && currentWinResultRef.current) {
          setTimeout(() => {
            highlightWinningSymbols(
              currentWinResultRef.current!.winType,
              currentWinResultRef.current!.winMultiplier,
              currentWinResultRef.current!.winPositions
            );
          }, 200); // Small delay for better visual effect
        }

        console.log('[PixiSlotMockup] Enhanced spin animation complete:', { winType, winMultiplier, isWin });
      }
    });

    animationTimelineRef.current = masterTimeline;

    // Apply reel visibility before starting animation
    applyReelVisibility(maskControls);

    // Animate each reel with progressive timing using animation settings
    reelContainersRef.current.forEach((reel, reelIndex) => {
      const delay = reelIndex * 0.2; // Progressive delay for realistic effect
      const baseSpinDuration = 2.5 / animationSettings.speed; // Use speed setting
      const spinDuration = baseSpinDuration + delay; // Longer spin for later reels

      // Skip animation for hidden reels
      const isReelVisible = maskControls.enabled ? (maskControls.perReelEnabled && maskControls.perReelEnabled[reelIndex]) : true;
      if (!isReelVisible) {
        console.log(`[PixiSlotMockup] Skipping animation for hidden reel ${reelIndex}`);
        return;
      }

      // ISSUE #2 FIX: Create extra symbols using optimized approach
      const extraSymbols: PIXI.Sprite[] = [];
      const symbolHeight = 80; // Standard symbol height
      const symbolSpacing = symbolHeight + 4; // Small padding between symbols

      // Add extra symbols above the visible area for smooth scrolling
      for (let i = 0; i < rows * 3; i++) { // Reduced from 4x to 3x for better performance
        try {
          let symbolSprite: PIXI.Sprite;

          // OPTIMIZATION: Use texture pool if available, fallback to original method
          if (texturePoolRef.current.length > 0) {
            const randomIndex = Math.floor(Math.random() * texturePoolRef.current.length);
            symbolSprite = new PIXI.Sprite(texturePoolRef.current[randomIndex]);
          } else {
            const randomSymbol = displaySymbols[Math.floor(Math.random() * displaySymbols.length)];
            symbolSprite = PIXI.Sprite.from(randomSymbol);
          }

          symbolSprite.width = symbolHeight * 0.8;
          symbolSprite.height = symbolHeight * 0.8;
          symbolSprite.x = (reel.children[0] as PIXI.Sprite).width * 0.1; // Center horizontally
          symbolSprite.y = -symbolSpacing * (i + 1); // Position above visible area

          // Mark as symbol sprite for easy identification
          (symbolSprite as any).isSymbolSprite = true;

          reel.addChild(symbolSprite);
          extraSymbols.push(symbolSprite);
        } catch (error) {
          console.warn('[PixiSlotMockup] Failed to create extra symbol:', error);
        }
      }

      // Create reel-specific timeline
      const reelTimeline = gsap.timeline({ delay });

      // ISSUE #3 FIX: Phase 1 - Premium acceleration with smooth start
      reelTimeline.to(reel, {
        y: symbolSpacing * 3,
        duration: 0.5 / animationSettings.speed, // Respect speed setting
        ease: PREMIUM_EASING.acceleration, // Premium smooth acceleration curve
        onUpdate: function() {
          wrapSymbols(reel, symbolSpacing, rows, extraSymbols.length, displaySymbols);
        }
      });

      // ISSUE #3 FIX: Phase 2 - Constant high speed with linear motion
      reelTimeline.to(reel, {
        y: `+=${symbolSpacing * 15}`, // Spin many symbols at high speed
        duration: spinDuration - 1,
        ease: PREMIUM_EASING.constantSpeed, // Linear motion for consistent spinning
        onStart: () => {
          // Apply motion blur during spinning if enabled
          if (animationSettings.visualEffects.spinBlur && animationSettings.blurIntensity > 0) {
            const blurFilter = new PIXI.BlurFilter();
            blurFilter.blur = animationSettings.blurIntensity;
            reel.filters = [blurFilter];
            console.log('[PixiSlotMockup] Applied motion blur:', animationSettings.blurIntensity);
          }

          // Apply glow effects if enabled (using ColorMatrix filter for glow-like effect)
          if (animationSettings.visualEffects.glowEffects) {
            reel.children.forEach(child => {
              if (child instanceof PIXI.Sprite && (child as any).isSymbolSprite === true) {
                const colorMatrix = new PIXI.ColorMatrixFilter();
                colorMatrix.brightness(1.3, false); // Increase brightness for glow effect
                colorMatrix.saturate(1.2, false); // Increase saturation
                child.filters = child.filters ? [...child.filters, colorMatrix] : [colorMatrix];
              }
            });
            console.log('[PixiSlotMockup] Applied glow effects to reel', reelIndex);
          }

          // Apply screen shake if enabled
          if (animationSettings.visualEffects.screenShake && appRef.current) {
            const originalX = appRef.current.stage.x;
            const originalY = appRef.current.stage.y;
            gsap.to(appRef.current.stage, {
              x: originalX + (Math.random() - 0.5) * 10,
              y: originalY + (Math.random() - 0.5) * 10,
              duration: 0.1,
              repeat: 5,
              yoyo: true,
              ease: "power2.inOut",
              onComplete: () => {
                if (appRef.current) {
                  appRef.current.stage.x = originalX;
                  appRef.current.stage.y = originalY;
                }
              }
            });
            console.log('[PixiSlotMockup] Applied screen shake effect');
          }
        },
        onUpdate: function() {
          wrapSymbols(reel, symbolSpacing, rows, extraSymbols.length, displaySymbols);
        }
      });

      // ISSUE #3 FIX: Phase 3 - Premium deceleration with smooth stop
      const isSpecialWin = winType === 'big-win' || winType === 'mega-win' || winType === 'freespins';
      const decelerationEasing = isSpecialWin ? PREMIUM_EASING.anticipation : PREMIUM_EASING.deceleration;
      console.log('[PixiSlotMockup] Using premium easing for reel', reelIndex, ':', decelerationEasing);

      // Calculate the exact final position where symbols should stop
      // Stop at a position that aligns symbols properly in the grid
      const finalPosition = -(reelIndex * 2) % symbolSpacing; // Small offset per reel for natural look

      reelTimeline.to(reel, {
        y: finalPosition, // Stop at calculated final position instead of 0
        duration: 0.5 / animationSettings.speed, // Use speed setting for deceleration too
        ease: decelerationEasing, // Premium easing with anticipation for special wins
        onStart: () => {
          // Remove motion blur as reel starts to decelerate
          if (reel.filters) {
            reel.filters = null;
            console.log('[PixiSlotMockup] Removed motion blur during deceleration');
          }

          // Remove glow effects from symbols
          reel.children.forEach(child => {
            if (child instanceof PIXI.Sprite && (child as any).isSymbolSprite === true) {
              child.filters = null;
            }
          });
          console.log('[PixiSlotMockup] Removed visual effects during deceleration');
        },
        onUpdate: function() {
          // When deceleration is 80% complete, start transitioning to final symbols
          if (this.progress() >= 0.8 && !reelSymbolsSetRef.current[reelIndex]) {
            // Set final symbols during the last part of deceleration
            setFinalReelSymbols(reel, reelIndex, currentSpinResultRef.current || undefined);
          }
        },
        onComplete: () => {
          // ISSUE #3 FIX: Add subtle bounce effect for premium feel
          const bounceTimeline = gsap.timeline();

          // Small overshoot
          bounceTimeline.to(reel, {
            y: finalPosition - 3, // Slight overshoot upward
            duration: 0.08,
            ease: PREMIUM_EASING.bounce
          });

          // Settle back to final position
          bounceTimeline.to(reel, {
            y: finalPosition,
            duration: 0.12,
            ease: "power2.out",
            onComplete: () => {
              // Clean up extra symbols after bounce completes
              extraSymbols.forEach(sprite => {
                if (sprite.parent) {
                  sprite.parent.removeChild(sprite);
                  sprite.destroy();
                }
              });

              // Ensure final symbols are set and force re-render
              setFinalReelSymbols(reel, reelIndex, currentSpinResultRef.current || undefined);

              // Force a re-render of the slot machine to show new symbols
              if (appRef.current && reelIndex === cols - 1) { // Only on last reel
                console.log(`[PixiSlotMockup] All reels complete with premium bounce, forcing re-render`);
                setTimeout(() => {
                  if (appRef.current) {
                    renderSlotMachine(appRef.current);
                  }
                }, 100);
              }

              console.log(`[PixiSlotMockup] Reel ${reelIndex} premium animation complete with bounce`);
            }
          });
        }
      });

      // Add reel timeline to master timeline
      masterTimeline.add(reelTimeline, 0);
    });
  }, [cols, rows, onWin, onBalanceChange, externalIsSpinning, externalBalance, internalBalance, animationSettings, maskControls]);

  // ISSUE #2 FIX: Optimized symbol wrapping with texture pooling
  const wrapSymbols = (reel: PIXI.Container, symbolSpacing: number, rows: number, extraSymbolsCount: number, displaySymbols: string[]) => {
    reel.children.forEach((child) => {
      if (child instanceof PIXI.Sprite && child.y > symbolSpacing * rows) {
        child.y -= symbolSpacing * (rows + extraSymbolsCount);

        // OPTIMIZATION: Use pre-loaded texture pool instead of creating new textures
        try {
          if (texturePoolRef.current.length > 0) {
            // Use texture from pool for smooth performance
            const randomIndex = Math.floor(Math.random() * texturePoolRef.current.length);
            child.texture = texturePoolRef.current[randomIndex];
          } else {
            // Fallback to original method if pool isn't ready
            const randomSymbol = displaySymbols[Math.floor(Math.random() * displaySymbols.length)];
            child.texture = PIXI.Texture.from(randomSymbol);
          }
        } catch (error) {
          console.warn('[PixiSlotMockup] Failed to update symbol texture:', error);
          // Additional fallback: keep current texture if update fails
        }
      }
    });
  };

  // Win highlighting functionality (from UnifiedSlotPreview/GameEngine)
  const highlightWinningSymbols = useCallback((winType: string, winMultiplier: number, winPositions?: { reel: number; row: number }[]) => {
    if (!appRef.current || winMultiplier <= 0) return;

    console.log('[PixiSlotMockup] Highlighting winning symbols:', { winType, winMultiplier, positions: winPositions?.length });

    // Use provided win positions or fallback to simple detection
    let positionsToHighlight = winPositions;
    if (!positionsToHighlight || positionsToHighlight.length === 0) {
      // Fallback: highlight middle row based on win type
      const middleRow = Math.floor(rows / 2);
      positionsToHighlight = [];

      // Determine how many symbols to highlight based on win type
      let symbolsToHighlight = 3; // Default
      if (winType === 'big-win') symbolsToHighlight = 4;
      else if (winType === 'mega-win' || winType === 'jackpot') symbolsToHighlight = 5;

      // Create win positions for middle row
      for (let reel = 0; reel < Math.min(symbolsToHighlight, cols); reel++) {
        positionsToHighlight.push({ reel, row: middleRow });
      }
    }

    // Apply green glow highlighting to winning symbols
    positionsToHighlight.forEach(pos => {
      const reelContainer = reelContainersRef.current[pos.reel];
      if (!reelContainer) return;

      // Find the symbol sprite at this position
      const symbolSprites = reelContainer.children.filter(child =>
        child instanceof PIXI.Sprite && (child as any).isSymbolSprite === true
      ) as PIXI.Sprite[];

      const symbolSprite = symbolSprites[pos.row];
      if (!symbolSprite) return;

      // Create green glow effect (similar to UnifiedSlotPreview)
      const glowFilter = new GlowFilter({
        color: winType === 'jackpot' ? 0xFFD700 : 0x00FF00, // Gold for jackpot, green for others
        outerStrength: 3,
        innerStrength: 1,
        quality: 0.5,
        distance: 15
      });

      symbolSprite.filters = [glowFilter];

      // Pulse animation
      gsap.to(symbolSprite.scale, {
        x: symbolSprite.scale.x * 1.2,
        y: symbolSprite.scale.y * 1.2,
        duration: 0.3,
        ease: "power2.inOut",
        yoyo: true,
        repeat: 5,
        onComplete: () => {
          // Clear highlighting after animation
          symbolSprite.filters = [];
          symbolSprite.scale.set(symbolSprite.scale.x / 1.2, symbolSprite.scale.y / 1.2);
        }
      });
    });

    console.log(`[PixiSlotMockup] Highlighted ${positionsToHighlight.length} winning symbols with green glow`);
  }, [cols, rows]);

  // Enhanced win detection system (from GameEngine)
  const detectWins = useCallback((symbols: string[][]): { hasWin: boolean; winType: string; winMultiplier: number; winPositions: { reel: number; row: number }[] } => {
    if (!symbols || symbols.length === 0) {
      return { hasWin: false, winType: 'none', winMultiplier: 0, winPositions: [] };
    }

    // Check middle line for wins (like GameEngine)
    const middleRow = Math.floor(rows / 2);
    const winPositions: { reel: number; row: number }[] = [];

    // Get first symbol on middle line
    const firstSymbol = symbols[0]?.[middleRow];
    if (!firstSymbol) {
      return { hasWin: false, winType: 'none', winMultiplier: 0, winPositions: [] };
    }

    let matchCount = 1;
    winPositions.push({ reel: 0, row: middleRow });

    // Check consecutive matching symbols (including wilds - handle both naming conventions)
    for (let reel = 1; reel < Math.min(cols, symbols.length); reel++) {
      const currentSymbol = symbols[reel]?.[middleRow];
      const isWild = (symbol: string) => symbol === 'wild' || symbol === 'wild_2' || symbol === 'wild2';

      if (currentSymbol === firstSymbol || isWild(currentSymbol) || isWild(firstSymbol)) {
        matchCount++;
        winPositions.push({ reel, row: middleRow });
      } else {
        break; // Stop at first non-matching symbol
      }
    }

    // Determine win type and multiplier based on match count
    if (matchCount >= 3) {
      let winType = 'small-win';
      let winMultiplier = matchCount * 2; // Base multiplier

      if (matchCount === 5) {
        winType = firstSymbol === 'wild' ? 'mega-win' : 'big-win';
        winMultiplier = firstSymbol === 'wild' ? 100 : 50;
      } else if (matchCount === 4) {
        winType = 'big-win';
        winMultiplier = 25;
      } else if (matchCount === 3) {
        winType = 'small-win';
        winMultiplier = 10;
      }

      // Special case for scatter symbols
      if (firstSymbol === 'scatter' && matchCount >= 3) {
        winType = 'freespins';
        winMultiplier = 0; // Free spins don't pay, but trigger feature
      }

      // Special case for wild symbols - higher multipliers
      const isWildWin = firstSymbol === 'wild' || firstSymbol === 'wild_2' || firstSymbol === 'wild2';
      if (isWildWin && matchCount >= 3) {
        winMultiplier = Math.floor(winMultiplier * 1.5); // 50% bonus for wild wins
      }

      console.log(`[PixiSlotMockup] Win detected: ${matchCount}x ${firstSymbol} = ${winType} (${winMultiplier}x)`);
      return { hasWin: true, winType, winMultiplier, winPositions };
    }

    return { hasWin: false, winType: 'none', winMultiplier: 0, winPositions: [] };
  }, [cols, rows]);

  // Helper function to set final symbols after spin completes
  // Helper function to set final symbols after spin completes (using predetermined result)
  const setFinalReelSymbols = (reel: PIXI.Container, reelIndex: number, spinResult?: string[][]) => {
    // Prevent multiple calls for the same reel during the same spin
    if (reelSymbolsSetRef.current[reelIndex]) {
      console.log(`[PixiSlotMockup] Final symbols already set for reel ${reelIndex}, skipping`);
      return;
    }

    // Filter for symbol sprites using the marker we added during creation
    const symbolSprites = reel.children.filter(child => {
      return child instanceof PIXI.Sprite && (child as any).isSymbolSprite === true;
    }) as PIXI.Sprite[];

    console.log('[PixiSlotMockup] Setting final symbols for reel', reelIndex, ':', symbolSprites.length);
    const reelFinalSymbols: string[] = [];

    // Use predetermined spin result if available
    if (spinResult && spinResult[reelIndex]) {
      const reelSymbols = spinResult[reelIndex];
      console.log(`[PixiSlotMockup] Applying predetermined symbols for reel ${reelIndex}:`, reelSymbols);

      symbolSprites.forEach((sprite, index) => {
        if (index < reelSymbols.length) {
          const targetSymbol = reelSymbols[index];
          try {
            sprite.texture = PIXI.Texture.from(targetSymbol);
            reelFinalSymbols.push(targetSymbol);
            console.log(`[PixiSlotMockup] ✓ Set reel ${reelIndex} row ${index} to: ${targetSymbol}`);
          } catch (error) {
            console.warn('[PixiSlotMockup] Failed to set predetermined symbol:', error);
            reelFinalSymbols.push('unknown');
          }
        }
      });
    } else {
      console.log('[PixiSlotMockup] No spin result provided, keeping current symbols for reel', reelIndex);
      symbolSprites.forEach((sprite, index) => {
        reelFinalSymbols.push('current');
      });
    }

    // Store final symbols for this reel and trigger re-render
    setFinalSpinSymbols(prev => {
      const newSymbols = [...prev];
      newSymbols[reelIndex] = reelFinalSymbols;
      console.log(`[PixiSlotMockup] Updated finalSpinSymbols for reel ${reelIndex}:`, newSymbols);
      return newSymbols;
    });

    // Mark this reel as having its final symbols set
    reelSymbolsSetRef.current[reelIndex] = true;
  };

  // Listen for external spin triggers with enhanced win detection integration
  useEffect(() => {
    console.log('[PixiSlotMockup] Setting up enhanced event listener:', { enableSpinAnimation, spinTriggerEvent });

    if (!enableSpinAnimation || !spinTriggerEvent) {
      console.log('[PixiSlotMockup] Event listener not set up - missing config');
      return;
    }

    const handleExternalSpin = (event: CustomEvent) => {
      console.log('[PixiSlotMockup] Enhanced external spin event received:', spinTriggerEvent, event.detail);
      
      // Check if we have a predetermined spin result from PixiPreviewWrapper
      if (event.detail?.spinResult) {
        console.log('[PixiSlotMockup] Using predetermined spin result:', event.detail.spinResult);
        // Store the predetermined result for the animation to use
        currentSpinResultRef.current = event.detail.spinResult.symbols;
        currentWinResultRef.current = {
          winType: event.detail.spinResult.winType || 'none',
          winMultiplier: event.detail.spinResult.totalWin || 0,
          winPositions: event.detail.spinResult.winPositions || []
        };
        
        // Start animation with predetermined result
        animateReelsEnhanced(
          event.detail.spinResult.winType || 'none',
          event.detail.spinResult.totalWin || 0,
          event.detail.spinResult.symbols
        );
      } else {
        // Fallback to original spin logic
        handleSpin();
      }
    };

    window.addEventListener(spinTriggerEvent, handleExternalSpin as EventListener);
    console.log('[PixiSlotMockup] Enhanced event listener added for:', spinTriggerEvent);

    return () => {
      console.log('[PixiSlotMockup] Removing enhanced event listener for:', spinTriggerEvent);
      window.removeEventListener(spinTriggerEvent, handleExternalSpin as EventListener);
    };
  }, [enableSpinAnimation, spinTriggerEvent, handleSpin]);

  // Listen for Step7 Animation Studio spin events (slotSpin)
  useEffect(() => {
    const handleSlotSpin = (event: CustomEvent) => {
      console.log('[PixiSlotMockup] slotSpin event received from Animation Studio:', event.detail);
      handleSpin();
    };

    window.addEventListener('slotSpin', handleSlotSpin as EventListener);
    console.log('[PixiSlotMockup] Event listener added for slotSpin');

    return () => {
      console.log('[PixiSlotMockup] Removing event listener for slotSpin');
      window.removeEventListener('slotSpin', handleSlotSpin as EventListener);
    };
  }, [handleSpin]);

  // Apply reel visibility controls during animation
  const applyReelVisibility = useCallback((controls: any) => {
    console.log('[PixiSlotMockup] Applying reel visibility controls:', controls);

    // Find all reel containers
    const reelContainers = reelContainersRef.current;

    if (reelContainers.length === 0) {
      console.log('[PixiSlotMockup] No reel containers found for visibility control');
      return;
    }

    reelContainers.forEach((reel, reelIndex) => {
      if (!reel) return;

      // Show/hide reel based on checkbox state
      const isReelVisible = controls.perReelEnabled && controls.perReelEnabled[reelIndex];
      const shouldShowReel = controls.enabled ? isReelVisible : true; // If masking disabled, show all reels

      // Set reel visibility
      reel.visible = shouldShowReel;

      console.log(`[PixiSlotMockup] Reel ${reelIndex} visibility:`, shouldShowReel);

      // Apply debug visual indicator if enabled
      if (controls.debugVisible && !shouldShowReel) {
        // Create a debug overlay to show hidden reel area
        if (!reelMasksRef.current[reelIndex]) {
          const debugOverlay = new PIXI.Graphics();
          debugOverlay.beginFill(0xff0000, 0.3); // Semi-transparent red
          debugOverlay.drawRect(reel.x - 50, reel.y - 10, 100, 240);
          debugOverlay.endFill();
          reelMasksRef.current[reelIndex] = debugOverlay;
          if (appRef.current) {
            appRef.current.stage.addChild(debugOverlay);
          }
          console.log(`[PixiSlotMockup] Added debug overlay for hidden reel ${reelIndex}`);
        }
      } else {
        // Remove debug overlay
        const debugOverlay = reelMasksRef.current[reelIndex];
        if (debugOverlay && appRef.current) {
          appRef.current.stage.removeChild(debugOverlay);
          debugOverlay.destroy();
          reelMasksRef.current[reelIndex] = null;
          console.log(`[PixiSlotMockup] Removed debug overlay for reel ${reelIndex}`);
        }
      }
    });
  }, []);

  // Listen for Step7 Animation Studio settings changes (animationSettingsChanged)
  useEffect(() => {
    const handleAnimationSettingsChanged = (event: CustomEvent) => {
      console.log('[PixiSlotMockup] animationSettingsChanged event received from Animation Studio:', event.detail);
      const { settings } = event.detail;

      if (settings) {
        const updatedSettings = {
          ...animationSettings,
          ...settings
        };
        setAnimationSettings(updatedSettings);
        console.log('[PixiSlotMockup] Animation settings updated from:', animationSettings, 'to:', updatedSettings);

        // Log specific easing changes
        if (settings.easing && settings.easing !== animationSettings.easing) {
          console.log('[PixiSlotMockup] Easing changed from:', animationSettings.easing, 'to:', settings.easing);
        }
      }
    };

    window.addEventListener('animationSettingsChanged', handleAnimationSettingsChanged as EventListener);
    console.log('[PixiSlotMockup] Event listener added for animationSettingsChanged');

    return () => {
      console.log('[PixiSlotMockup] Removing event listener for animationSettingsChanged');
      window.removeEventListener('animationSettingsChanged', handleAnimationSettingsChanged as EventListener);
    };
  }, [animationSettings]);

  // Listen for Step7 Animation Studio mask control changes (applyMaskControls)
  useEffect(() => {
    const handleMaskControlsChanged = (event: CustomEvent) => {
      console.log('[PixiSlotMockup] applyMaskControls event received from Animation Studio:', event.detail);
      const { controls } = event.detail;

      if (controls) {
        setMaskControls(controls);
        console.log('[PixiSlotMockup] Mask controls updated:', controls);

        // Apply reel visibility controls
        applyReelVisibility(controls);
      }
    };

    window.addEventListener('applyMaskControls', handleMaskControlsChanged as EventListener);
    console.log('[PixiSlotMockup] Event listener added for applyMaskControls');

    return () => {
      console.log('[PixiSlotMockup] Removing event listener for applyMaskControls');
      window.removeEventListener('applyMaskControls', handleMaskControlsChanged as EventListener);
    };
  }, [applyReelVisibility]);

  // Apply initial reel visibility when component is ready
  useEffect(() => {
    if (isReady && reelContainersRef.current.length > 0) {
      applyReelVisibility(maskControls);
    }
  }, [isReady, applyReelVisibility, maskControls]);

  // Listen for Step7 Freespin Transition events (previewFreespinTransition)
  useEffect(() => {
    const handleFreespinTransition = (event: CustomEvent) => {
      console.log('🎰 [PixiSlotMockup] previewFreespinTransition event received from Animation Studio:', event.detail);
      const { direction, style, duration } = event.detail;

      // Debug: Log current config state
      console.log('🎰 [PixiSlotMockup] Current config:', {
        hasConfig: !!config,
        hasDerivedBackgrounds: !!(config as any)?.derivedBackgrounds,
        hasFreespinBackground: !!(config as any)?.derivedBackgrounds?.freespin,
        hasRegularBackground: !!config?.background?.backgroundImage,
        freespinUrl: (config as any)?.derivedBackgrounds?.freespin,
        regularUrl: config?.background?.backgroundImage
      });

      // Get freespin background from game config
      const freespinBackgroundUrl = (config as any)?.derivedBackgrounds?.freespin;
      const regularBackgroundUrl = config?.background?.backgroundImage;

      if (!freespinBackgroundUrl) {
        console.warn('🎰 [PixiSlotMockup] No freespin background found in config. Please generate one in Step 4.');
        alert('No freespin background found. Please generate a freespin background in Step 4 first.');
        return;
      }

      if (!regularBackgroundUrl) {
        console.warn('🎰 [PixiSlotMockup] No regular background found in config.');
        return;
      }

      console.log('🎰 [PixiSlotMockup] Performing freespin transition:', {
        direction,
        style,
        duration,
        freespinBackgroundUrl: freespinBackgroundUrl ? 'Available' : 'Missing',
        regularBackgroundUrl: regularBackgroundUrl ? 'Available' : 'Missing'
      });

      // Apply the background transition with style and duration
      performFreespinBackgroundTransition(direction, freespinBackgroundUrl, regularBackgroundUrl, duration, style);
    };

    window.addEventListener('previewFreespinTransition', handleFreespinTransition as EventListener);
    console.log('🎰 [PixiSlotMockup] Event listener added for previewFreespinTransition');

    return () => {
      console.log('🎰 [PixiSlotMockup] Removing event listener for previewFreespinTransition');
      window.removeEventListener('previewFreespinTransition', handleFreespinTransition as EventListener);
    };
  }, [config]);

  // Perform freespin background transition
  const performFreespinBackgroundTransition = useCallback((
    direction: 'to-freespin' | 'to-regular',
    freespinBackgroundUrl: string,
    regularBackgroundUrl: string,
    duration: number = 2.0,
    style: string = 'fade'
  ) => {
    console.log(`🎰 [PixiSlotMockup] performFreespinBackgroundTransition called:`, {
      direction,
      freespinBackgroundUrl,
      regularBackgroundUrl,
      duration,
      style,
      hasApp: !!appRef.current
    });

    if (!appRef.current) {
      console.warn('🎰 [PixiSlotMockup] PIXI app not ready for freespin transition');
      return;
    }

    console.log(`🎰 [PixiSlotMockup] Starting ${direction} transition with ${style} style for ${duration}s`);

    // Find the background sprite
    const backgroundSprite = appRef.current.stage.children.find(child =>
      child instanceof PIXI.Sprite && (child as any).isBackgroundSprite === true
    ) as PIXI.Sprite;

    if (!backgroundSprite) {
      console.warn('🎰 [PixiSlotMockup] Background sprite not found for freespin transition');
      return;
    }

    console.log('🎰 [PixiSlotMockup] Found background sprite:', backgroundSprite);

    // Determine target background URL
    const targetBackgroundUrl = direction === 'to-freespin' ? freespinBackgroundUrl : regularBackgroundUrl;
    console.log('🎰 [PixiSlotMockup] Target background URL:', targetBackgroundUrl);

    // Create new texture
    const newTexture = PIXI.Texture.from(targetBackgroundUrl);
    console.log('🎰 [PixiSlotMockup] New texture created:', newTexture);

    // Store original properties for restoration
    const originalAlpha = backgroundSprite.alpha;
    const originalScale = { x: backgroundSprite.scale.x, y: backgroundSprite.scale.y };
    const originalPosition = { x: backgroundSprite.x, y: backgroundSprite.y };

    // Apply transition based on style
    switch (style) {
      case 'fade':
        applyFadeTransition(backgroundSprite, newTexture, duration, originalAlpha);
        break;
      case 'slide':
        applySlideTransition(backgroundSprite, newTexture, duration, originalPosition);
        break;
      case 'zoom':
        applyZoomTransition(backgroundSprite, newTexture, duration, originalScale);
        break;
      case 'dissolve':
        applyDissolveTransition(backgroundSprite, newTexture, duration, originalAlpha);
        break;
      default:
        console.warn(`🎰 [PixiSlotMockup] Unknown transition style: ${style}, falling back to fade`);
        applyFadeTransition(backgroundSprite, newTexture, duration, originalAlpha);
    }

  }, []);

  // Individual transition effect functions
  const applyFadeTransition = useCallback((
    sprite: PIXI.Sprite,
    newTexture: PIXI.Texture,
    duration: number,
    originalAlpha: number
  ) => {
    console.log('🌅 [PixiSlotMockup] Applying fade transition');
    gsap.to(sprite, {
      alpha: 0,
      duration: duration / 2,
      ease: "power2.inOut",
      onComplete: () => {
        sprite.texture = newTexture;
        gsap.to(sprite, {
          alpha: originalAlpha,
          duration: duration / 2,
          ease: "power2.inOut",
          onComplete: () => {
            console.log('🌅 [PixiSlotMockup] Fade transition completed');
          }
        });
      }
    });
  }, []);

  const applySlideTransition = useCallback((
    sprite: PIXI.Sprite,
    newTexture: PIXI.Texture,
    duration: number,
    originalPosition: { x: number; y: number }
  ) => {
    console.log('➡️ [PixiSlotMockup] Applying seamless slide transition');

    if (!appRef.current) {
      console.warn('➡️ [PixiSlotMockup] PIXI app not ready for slide transition');
      return;
    }

    // Create a second sprite for the new background
    const newSprite = new PIXI.Sprite(newTexture);

    // Copy all properties from the original sprite
    newSprite.width = sprite.width;
    newSprite.height = sprite.height;
    newSprite.anchor.set(sprite.anchor.x, sprite.anchor.y);
    newSprite.y = sprite.y;
    newSprite.alpha = sprite.alpha;
    newSprite.scale.set(sprite.scale.x, sprite.scale.y);

    // Position the new sprite to the right of the current sprite (off-screen)
    newSprite.x = originalPosition.x + sprite.width;

    // Mark as background sprite for future transitions
    (newSprite as any).isBackgroundSprite = true;

    // Add the new sprite to the stage at the correct layer (behind symbols)
    // Find the index of the old background sprite to maintain proper layering
    const oldSpriteIndex = appRef.current.stage.getChildIndex(sprite);
    appRef.current.stage.addChildAt(newSprite, oldSpriteIndex);

    console.log('➡️ [PixiSlotMockup] New sprite added at layer index:', oldSpriteIndex);

    console.log('➡️ [PixiSlotMockup] Created new sprite positioned at:', newSprite.x);

    // Animate both sprites simultaneously for seamless transition
    const slideDistance = sprite.width; // Slide exactly one background width

    // Slide the old sprite to the left (off-screen)
    gsap.to(sprite, {
      x: originalPosition.x - slideDistance,
      duration: duration,
      ease: "power2.inOut",
      onComplete: () => {
        // Remove the old sprite from stage and destroy it
        if (sprite.parent) {
          sprite.parent.removeChild(sprite);
          sprite.destroy();
        }
        console.log('➡️ [PixiSlotMockup] Old sprite removed and destroyed');
      }
    });

    // Slide the new sprite to the center position
    gsap.to(newSprite, {
      x: originalPosition.x,
      duration: duration,
      ease: "power2.inOut",
      onComplete: () => {
        console.log('➡️ [PixiSlotMockup] Seamless slide transition completed');
      }
    });

  }, []);

  const applyZoomTransition = useCallback((
    sprite: PIXI.Sprite,
    newTexture: PIXI.Texture,
    duration: number,
    originalScale: { x: number; y: number }
  ) => {
    console.log('🔍 [PixiSlotMockup] Applying zoom transition');

    // Zoom out (scale down to 0)
    gsap.to(sprite.scale, {
      x: 0,
      y: 0,
      duration: duration / 2,
      ease: "power2.inOut",
      onComplete: () => {
        // Change texture
        sprite.texture = newTexture;

        // Zoom in (scale back to original)
        gsap.to(sprite.scale, {
          x: originalScale.x,
          y: originalScale.y,
          duration: duration / 2,
          ease: "back.out(1.7)",
          onComplete: () => {
            console.log('🔍 [PixiSlotMockup] Zoom transition completed');
          }
        });
      }
    });
  }, []);

  const applyDissolveTransition = useCallback((
    sprite: PIXI.Sprite,
    newTexture: PIXI.Texture,
    duration: number,
    originalAlpha: number
  ) => {
    console.log('💫 [PixiSlotMockup] Applying dissolve transition');

    // Create a more complex dissolve effect with multiple fade steps
    const steps = 8;
    const stepDuration = duration / steps;

    let currentStep = 0;

    const dissolveStep = () => {
      currentStep++;
      const targetAlpha = currentStep % 2 === 0 ? 0.3 : 0.8;

      gsap.to(sprite, {
        alpha: targetAlpha,
        duration: stepDuration,
        ease: "power1.inOut",
        onComplete: () => {
          if (currentStep === Math.floor(steps / 2)) {
            // Change texture at halfway point
            sprite.texture = newTexture;
          }

          if (currentStep < steps) {
            dissolveStep();
          } else {
            // Final fade to original alpha
            gsap.to(sprite, {
              alpha: originalAlpha,
              duration: stepDuration,
              ease: "power2.out",
              onComplete: () => {
                console.log('💫 [PixiSlotMockup] Dissolve transition completed');
              }
            });
          }
        }
      });
    };

    dissolveStep();
  }, []);

  // Debug component creation
  console.log('[PixiSlotMockup] Component created:', {
    cols, rows,
    isMobile,
    orientation,
    hasFrame: !!frame,
    frameUrl: frame
  });
  const [containerDimensions, setContainerDimensions] = useState({ width: 800, height: 600 });
  const [reelGapSettings, setReelGapSettings] = useState({
    gap: config.reelGap || 1,
    position: config.reelDividerPosition || { x: 0, y: 0 },
    stretch: config.reelDividerStretch || { x: 100, y: 100 }
  });
  const [forceUpdateKey, setForceUpdateKey] = useState(0);
  const [reelBackgrounds, setReelBackgrounds] = useState<string[]>([]);
  const [isGeneratingReel, setIsGeneratingReel] = useState<number | null>(null);
  const [isRendering, setIsRendering] = useState(false);

  // Auto-retrieve assets from store if not passed as props
  const finalLogo = logo || (config as any)?.logo || (config as any)?.gameName;
  const finalBackground = background || (config as any)?.background?.backgroundImage || (config as any)?.backgroundImage;
  const finalFrame = frame || (config as any)?.frame?.frameImage || (config as any)?.frameImage || (config as any)?.frame;
  const finalSymbols = symbols.length > 0 ? symbols : config?.theme?.generated?.symbols || [];
  const configReelBackgrounds = (config as any)?.individualReelBackgrounds || [];

  // ISSUE #2 FIX: Initialize texture pool when symbols change
  useEffect(() => {
    // Convert finalSymbols to array format for consistent handling
    const symbolsArray = Array.isArray(finalSymbols) ? finalSymbols :
                        (finalSymbols && typeof finalSymbols === 'object') ? Object.values(finalSymbols).filter(Boolean) : [];

    if (symbolsArray.length > 0) {
      console.log('[PixiSlotMockup] Initializing texture pool with', symbolsArray.length, 'symbols');

      // Pre-load all textures into pool for smooth symbol wrapping
      const textures: PIXI.Texture[] = [];
      symbolsArray.forEach((symbolUrl: any) => {
        try {
          const symbolUrlString = typeof symbolUrl === 'string' ? symbolUrl : symbolUrl?.url || symbolUrl?.imageUrl;
          if (symbolUrlString) {
            const texture = PIXI.Texture.from(symbolUrlString);
            textures.push(texture);
          }
        } catch (error) {
          console.warn('[PixiSlotMockup] Failed to load texture for symbol:', symbolUrl, error);
        }
      });

      texturePoolRef.current = textures;
      console.log('[PixiSlotMockup] Texture pool initialized with', textures.length, 'textures');
    }
  }, [finalSymbols]);

  // Debug frame loading
  React.useEffect(() => {
    console.log('[PixiSlotMockup] Frame debug:', {
      frameProp: frame,
      configFrame: (config as any)?.frame,
      configFrameImage: (config as any)?.frame?.frameImage,
      configFrameImageDirect: (config as any)?.frameImage,
      finalFrame: finalFrame
    });
  }, [frame, config, finalFrame]);

  // Logo drag state for interactive positioning
  const [isDraggingLogo, setIsDraggingLogo] = useState(false);
  const [isResizingLogo, setIsResizingLogo] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [startPosition, setStartPosition] = useState({ x: 0, y: 0 });
  const [startScale, setStartScale] = useState(100);

  // Initialize PixiJS application
  useEffect(() => {
    if (!containerRef.current || appRef.current) return;

    const initPixi = async () => {
      try {
        const rect = containerRef.current!.getBoundingClientRect();
        setContainerDimensions({ width: rect.width, height: rect.height });

        // Create PixiJS application
        const app = new PIXI.Application({
          width: rect.width,
          height: rect.height,
          backgroundColor: pixiMockupStyles.backgroundColor,
          antialias: true,
          resolution: window.devicePixelRatio || 1,
          autoDensity: true
        });

        // Add canvas to container
        containerRef.current!.appendChild(app.view as HTMLCanvasElement);
        
        // Style the canvas
        const canvas = app.view as HTMLCanvasElement;
        canvas.style.width = '100%';
        canvas.style.height = '100%';
        canvas.style.display = 'block';

        appRef.current = app;
        
        // Render the slot machine
        await renderSlotMachine(app);
        
        setIsReady(true);
        console.log('[PixiSlotMockup] PixiJS application initialized');
      } catch (error) {
        console.error('[PixiSlotMockup] Failed to initialize PixiJS:', error);
      }
    };

    initPixi();

    // Cleanup
    return () => {
      console.log('[PixiSlotMockup] Component cleanup:', {
        cols, rows,
        isMobile,
        orientation,
        hasFrame: !!frame
      });
      if (appRef.current) {
        appRef.current.destroy(true, true);
        appRef.current = null;
        setIsReady(false);
      }
    };
  }, []);

  // Listen for individual button updates
  useEffect(() => {
    const handleIndividualButtonsUpdate = (event: CustomEvent) => {
      console.log('[PixiSlotMockup] Received individual buttons update:', event.detail);
      if (appRef.current && isReady) {
        // Force re-render to show new buttons
        renderSlotMachine(appRef.current);
      }
    };

    window.addEventListener('individualButtonsUpdated', handleIndividualButtonsUpdate as EventListener);

    return () => {
      window.removeEventListener('individualButtonsUpdated', handleIndividualButtonsUpdate as EventListener);
    };
  }, [isReady]);

  // Listen for UI button adjustments (position, scale, visibility)
  useEffect(() => {
    const handleUIButtonAdjustments = (event: CustomEvent) => {
      console.log('[PixiSlotMockup] Received UI button adjustments:', event.detail);
      const { position, scale, visibility } = event.detail;

      // Update local state for UI button adjustments
      if (position) {
        setUiButtonAdjustments(prev => ({ ...prev, position }));
      }
      if (scale !== undefined) {
        setUiButtonAdjustments(prev => ({ ...prev, scale }));
      }
      if (visibility !== undefined) {
        setUiButtonAdjustments(prev => ({ ...prev, visibility }));
      }

      if (appRef.current && isReady) {
        // Force re-render to apply button adjustments
        renderSlotMachine(appRef.current);
      }
    };

    window.addEventListener('uiButtonAdjustmentsUpdated', handleUIButtonAdjustments as EventListener);

    return () => {
      window.removeEventListener('uiButtonAdjustmentsUpdated', handleUIButtonAdjustments as EventListener);
    };
  }, [isReady]);

  // Listen for background updates from Step 6
  useEffect(() => {
    const handleBackgroundUpdate = (event: CustomEvent) => {
      console.log('[PixiSlotMockup] Received background update:', event.detail);
      if (appRef.current && isReady) {
        // Force re-render to show new background
        renderSlotMachine(appRef.current);
      }
    };

    window.addEventListener('backgroundUpdated', handleBackgroundUpdate as EventListener);

    return () => {
      window.removeEventListener('backgroundUpdated', handleBackgroundUpdate as EventListener);
    };
  }, [isReady]);

  // Listen for reel gap adjustments from Step 6
  useEffect(() => {
    const handleReelGapUpdate = (event: CustomEvent) => {
      console.log('[PixiSlotMockup] Received reel gap update:', event.detail);
      const { gap, position, stretch, updateType } = event.detail;

      // Update local state for immediate UI feedback
      setReelGapSettings({
        gap: gap || 2,
        position: position || { x: 0, y: 0 },
        stretch: stretch || { x: 100, y: 100 }
      });

      if (appRef.current && isReady) {
        // Only update reel dividers for reel adjustments, not full re-render
        if (updateType === 'reel-adjustment') {
          console.log('[PixiSlotMockup] Selective update: refreshing reel dividers for gap adjustment');
          
          const { width: containerWidth, height: containerHeight } = containerDimensions;
          const gridDimensions = calculatePixiMockupDimensions({
            cols,
            rows,
            containerWidth,
            containerHeight,
            isMobile,
            orientation
          });
          
          // Only recreate reel dividers
          createReelDividers(appRef.current, gridDimensions);
        } else {
          // Full re-render for other updates
          setTimeout(() => {
            if (appRef.current) {
              renderSlotMachine(appRef.current);
            }
          }, 50);
        }
      }
    };

    window.addEventListener('reelGapAdjustmentsUpdated', handleReelGapUpdate as EventListener);

    return () => {
      window.removeEventListener('reelGapAdjustmentsUpdated', handleReelGapUpdate as EventListener);
    };
  }, [isReady, cols, rows, containerDimensions, isMobile, orientation]);

  // Listen for AI reel image updates to trigger selective re-render
  useEffect(() => {
    const handleAIReelUpdate = (event: CustomEvent) => {
      console.log('[PixiSlotMockup] AI reel image updated, updating reel dividers only', event.detail);
      if (appRef.current && isReady) {
        const { updateType } = event.detail;
        
        // Only update reel dividers instead of full re-render for better performance
        if (updateType === 'reel-only' || updateType === 'reel-adjustment') {
          console.log('[PixiSlotMockup] Selective update: refreshing reel dividers only');
          
          // Calculate grid dimensions for reel divider update
          const { width: containerWidth, height: containerHeight } = containerDimensions;
          const gridDimensions = calculatePixiMockupDimensions({
            cols,
            rows,
            containerWidth,
            containerHeight,
            isMobile,
            orientation
          });
          
          // Only recreate reel dividers
          createReelDividers(appRef.current, gridDimensions);
        } else {
          // Full re-render for other types of updates
          setTimeout(() => {
            if (appRef.current) {
              renderSlotMachine(appRef.current);
            }
          }, 100);
        }
      }
    };

    window.addEventListener('aiReelImageUpdated', handleAIReelUpdate as EventListener);

    return () => {
      window.removeEventListener('aiReelImageUpdated', handleAIReelUpdate as EventListener);
    };
  }, [isReady, cols, rows, containerDimensions, isMobile, orientation]);

  // Listen for logo position/scale changes to update only the logo
  useEffect(() => {
    const handleLogoPositionChange = (event: CustomEvent) => {
      console.log('[PixiSlotMockup] Logo position changed, updating logo only', event.detail);
      if (appRef.current && isReady) {
        // Only re-create the logo instead of full re-render for better performance
        createLogo(appRef.current);
      }
    };

    const handleLogoScaleChange = (event: CustomEvent) => {
      console.log('[PixiSlotMockup] Logo scale changed, updating logo only', event.detail);
      if (appRef.current && isReady) {
        // Only re-create the logo instead of full re-render for better performance
        createLogo(appRef.current);
      }
    };

    window.addEventListener('logoPositionChanged', handleLogoPositionChange as EventListener);
    window.addEventListener('logoScaleChanged', handleLogoScaleChange as EventListener);

    return () => {
      window.removeEventListener('logoPositionChanged', handleLogoPositionChange as EventListener);
      window.removeEventListener('logoScaleChanged', handleLogoScaleChange as EventListener);
    };
  }, [isReady]);

  // Initialize reel gap settings from config only once on mount
  // Don't sync continuously to avoid overriding user adjustments
  useEffect(() => {
    const initialReelGapSettings = {
      gap: config.reelGap || 1,
      position: config.reelDividerPosition || { x: 0, y: 0 },
      stretch: config.reelDividerStretch || { x: 100, y: 100 }
    };

    console.log('[PixiSlotMockup] Initializing reel gap settings from config:', initialReelGapSettings);
    setReelGapSettings(initialReelGapSettings);
  }, []); // Empty dependency array - only run once on mount

  // Sync reel backgrounds from config
  useEffect(() => {
    if (configReelBackgrounds.length > 0) {
      console.log('[PixiSlotMockup] Syncing reel backgrounds from config:', configReelBackgrounds);
      setReelBackgrounds(configReelBackgrounds);
    }
  }, [configReelBackgrounds]);

  // Generate individual reel background
  const generateReelBackground = async (reelIndex: number) => {
    if (isGeneratingReel !== null) {
      console.log('Already generating a reel, please wait...');
      return;
    }

    setIsGeneratingReel(reelIndex);
    console.log(`[PixiSlotMockup] Generating background for reel ${reelIndex + 1}`);

    try {
      // Get theme information
      const themeName = typeof config.theme === 'string'
        ? config.theme
        : (config.theme?.mainTheme || config.theme?.name || 'casino');

      // Get current grid dimensions
      const totalReels = cols;
      const totalRows = rows;

      // Create enhanced prompt for individual reel strip
      const enhancedPrompt = `Create a vertical reel strip background for a ${themeName} themed slot machine game.

      REEL SPECIFICATIONS:
      - This is reel ${reelIndex + 1} of ${totalReels} total reels
      - Grid layout: ${totalReels} columns × ${totalRows} rows
      - Vertical strip design optimized for ${totalRows} symbol positions
      - Will be tiled/repeated vertically to fill the reel height

      VISUAL REQUIREMENTS:
      - Vertical gradient or pattern that complements ${themeName} theme
      - Seamless tiling capability (top and bottom edges should connect smoothly)
      - Subtle texture that enhances symbol visibility without interference
      - ${totalRows} distinct visual sections for symbol placement
      - Professional casino-quality appearance

      DESIGN ELEMENTS:
      - Colors that complement the ${themeName} theme
      - Subtle vertical lines or patterns
      - Appropriate lighting that doesn't compete with symbols
      - Metallic or material finishes appropriate to theme
      - Elegant, refined appearance

      TECHNICAL SPECIFICATIONS:
      - Vertical orientation (taller than wide, aspect ratio 1:4 or 1:3)
      - PNG format with transparency support where appropriate
      - High contrast areas for optimal symbol visibility
      - Seamless vertical tiling capability
      - Optimized for ${totalRows} symbol rows

      TILING REQUIREMENTS:
      - Top edge must seamlessly connect with bottom edge when repeated
      - Pattern should flow naturally when tiled vertically
      - No visible seams or breaks when repeated multiple times

      Create a professional reel strip background that will tile beautifully and enhance the ${themeName} slot machine experience!`;

      // Import the enhanced OpenAI client
      const enhancedOpenaiClientModule = await import('../../utils/enhancedOpenaiClient');
      const enhancedOpenaiClient = enhancedOpenaiClientModule.enhancedOpenaiClient;

      // Generate the reel background
      const result = await enhancedOpenaiClient.generateImageWithConfig({
        prompt: enhancedPrompt,
        targetSymbolId: `reel_background_${reelIndex}`,
        gameId: config.gameId || 'slot_game',
        count: 1
      });

      if (result && result.success && result.images && result.images.length > 0) {
        const imageUrl = result.images[0];

        // Update reel backgrounds array
        const newReelBackgrounds = [...reelBackgrounds];
        newReelBackgrounds[reelIndex] = imageUrl;
        setReelBackgrounds(newReelBackgrounds);

        // Dispatch event to notify Step 6 about the new reel background
        window.dispatchEvent(new CustomEvent('reelBackgroundGenerated', {
          detail: {
            reelIndex,
            imageUrl,
            reelBackgrounds: newReelBackgrounds
          }
        }));

        console.log(`[PixiSlotMockup] Generated background for reel ${reelIndex + 1}:`, imageUrl);

        // Force re-render to show the new reel background
        if (appRef.current) {
          renderSlotMachine(appRef.current);
        }
      } else {
        console.error(`Failed to generate background for reel ${reelIndex + 1}`);
        alert(`Failed to generate background for reel ${reelIndex + 1}. Please try again.`);
      }
    } catch (error) {
      console.error(`Error generating reel ${reelIndex + 1} background:`, error);
      alert(`Error generating reel ${reelIndex + 1} background. Please try again.`);
    } finally {
      setIsGeneratingReel(null);
    }
  };

  // Update container dimensions on resize and view mode changes
  useEffect(() => {
    const updateDimensions = () => {
      if (containerRef.current && appRef.current) {
        const rect = containerRef.current.getBoundingClientRect();
        const newDimensions = { width: rect.width, height: rect.height };

        // Always update if app is ready, even if dimensions seem the same
        // This handles cases where the container changes between steps
        if (isReady && (newDimensions.width > 0 && newDimensions.height > 0)) {
          console.log('[PixiSlotMockup] Updating dimensions:', {
            old: containerDimensions,
            new: newDimensions,
            changed: newDimensions.width !== containerDimensions.width || newDimensions.height !== containerDimensions.height
          });

          setContainerDimensions(newDimensions);

          // Resize PixiJS application
          appRef.current.renderer.resize(newDimensions.width, newDimensions.height);

          // Force re-render to ensure UI is positioned correctly
          setTimeout(() => {
            if (appRef.current) {
              renderSlotMachine(appRef.current);
            }
          }, 50); // Small delay to ensure resize is complete
        }
      }
    };

    // Initial update
    updateDimensions();

    // Listen for resize events
    window.addEventListener('resize', updateDimensions);

    // Use ResizeObserver for more accurate container size tracking
    let resizeObserver: ResizeObserver | null = null;
    if (containerRef.current) {
      resizeObserver = new ResizeObserver(() => {
        // Debounce the resize observer
        setTimeout(updateDimensions, 10);
      });
      resizeObserver.observe(containerRef.current);
    }

    // Also update on visibility change (when switching between steps)
    const handleVisibilityChange = () => {
      if (!document.hidden && appRef.current && isReady) {
        console.log('[PixiSlotMockup] Visibility changed, checking if update needed...');
        // Only update dimensions if container size actually changed
        // This prevents unnecessary re-renders that reset logo position
        if (containerRef.current) {
          const rect = containerRef.current.getBoundingClientRect();
          const currentDims = { width: rect.width, height: rect.height };
          const hasChanged = currentDims.width !== containerDimensions.width ||
                           currentDims.height !== containerDimensions.height;

          if (hasChanged && currentDims.width > 0 && currentDims.height > 0) {
            console.log('[PixiSlotMockup] Container dimensions changed, updating...');
            setTimeout(updateDimensions, 100);
          } else {
            console.log('[PixiSlotMockup] Container dimensions unchanged, skipping update');
          }
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      window.removeEventListener('resize', updateDimensions);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      if (resizeObserver) {
        resizeObserver.disconnect();
      }
    };
  }, [containerDimensions.width, containerDimensions.height, isReady]);

  // Force re-render when view mode related props change
  useEffect(() => {
    if (appRef.current && isReady) {
      console.log('[PixiSlotMockup] View mode changed, re-rendering...');
      renderSlotMachine(appRef.current);
    }
  }, [isMobile, orientation, isReady]);

  // Listen for step navigation events (same as CSS version)
  useEffect(() => {
    const handleSymbolsChanged = (event: any) => {
      console.log('[PixiSlotMockup] Symbols changed event received:', event.detail);
      if (appRef.current && isReady) {
        renderSlotMachine(appRef.current);
      }
    };

    const handleLayoutChanged = (event: any) => {
      console.log('[PixiSlotMockup] Layout changed event received:', event.detail);
      if (appRef.current && isReady) {
        renderSlotMachine(appRef.current);
      }
    };

    // Listen for step navigation events
    window.addEventListener('symbolsChanged', handleSymbolsChanged);
    window.addEventListener('layoutChanged', handleLayoutChanged);

    return () => {
      window.removeEventListener('symbolsChanged', handleSymbolsChanged);
      window.removeEventListener('layoutChanged', handleLayoutChanged);
    };
  }, [isReady]);

  // Force update when key props change (indicating step navigation)
  useEffect(() => {
    console.log('[PixiSlotMockup] Key props changed, forcing update...');
    setForceUpdateKey(prev => prev + 1);

    // Force a complete re-render after a short delay
    if (appRef.current && isReady) {
      setTimeout(() => {
        if (appRef.current && containerRef.current) {
          const rect = containerRef.current.getBoundingClientRect();
          if (rect.width > 0 && rect.height > 0) {
            setContainerDimensions({ width: rect.width, height: rect.height });
            appRef.current.renderer.resize(rect.width, rect.height);
            renderSlotMachine(appRef.current);
          }
        }
      }, 100);
    }
  }, [cols, rows, showControls, isMobile, orientation, isReady]);

  // Re-render when props change OR when navigating between steps
  // Note: logoPosition and logoScale removed from deps since we now get latest values from store
  useEffect(() => {
    if (appRef.current && isReady) {
      console.log('[PixiSlotMockup] Props changed, re-rendering...', {
        cols, rows,
        symbolsCount: finalSymbols.length,
        hasBackground: !!finalBackground,
        hasFrame: !!finalFrame,
        hasLogo: !!finalLogo,
        containerDimensions
      });
      renderSlotMachine(appRef.current);
    }
  }, [cols, rows, finalSymbols, finalSpinSymbols, finalBackground, finalFrame, finalLogo, gridAdjustments, frameAdjustments, backgroundAdjustments, uiButtonAdjustments, reelGapSettings, reelBackgrounds, isGeneratingReel, isReady, containerDimensions, config.aiReelImage]);

  // Create default UI buttons (fallback when no AI buttons are generated)
  const createDefaultUIButtons = (app: PIXI.Application) => {
    const defaultButtons = [
      { name: 'SPIN', color: 0xFFD700, x: 0, size: 80, icon: '▶' },
      { name: 'AUTO', color: 0x4169E1, x: 90, size: 60, icon: '⟲' },
      { name: 'MENU', color: 0xFF6347, x: 160, size: 60, icon: '☰' },
      { name: 'SOUND', color: 0x32CD32, x: 230, size: 60, icon: '♪' },
      { name: 'SETTINGS', color: 0x9370DB, x: 300, size: 60, icon: '⚙' }
    ];

    const buttonY = containerDimensions.height - 80;
    const startX = (containerDimensions.width - 360) / 2; // Center the button group

    defaultButtons.forEach((buttonDef) => {
      const button = new PIXI.Graphics();
      button.name = `ui-button-${buttonDef.name.toLowerCase()}`;

      // Draw circular button
      button.beginFill(buttonDef.color, 0.8);
      button.drawCircle(0, 0, buttonDef.size / 2);
      button.endFill();

      // Add border
      button.lineStyle(2, 0xFFFFFF, 0.5);
      button.drawCircle(0, 0, buttonDef.size / 2);

      // Position button
      button.x = startX + buttonDef.x;
      button.y = buttonY;

      // Apply UI button adjustments
      if (uiButtonAdjustments) {
        const { position, scale } = uiButtonAdjustments;

        if (position) {
          button.x += position.x;
          button.y += position.y;
        }

        if (scale && scale !== 100) {
          const scaleValue = scale / 100;
          button.scale.set(scaleValue);
        }
      }

      // Add icon and text label
      const iconText = new PIXI.Text(buttonDef.icon, {
        fontFamily: 'Arial',
        fontSize: buttonDef.size / 3,
        fill: 0xFFFFFF,
        fontWeight: 'bold'
      });
      iconText.anchor.set(0.5, 0.3);
      button.addChild(iconText);

      // Add smaller text label below icon
      const labelText = new PIXI.Text(buttonDef.name, {
        fontFamily: 'Arial',
        fontSize: buttonDef.size / 6,
        fill: 0xFFFFFF,
        fontWeight: 'normal'
      });
      labelText.anchor.set(0.5, -0.3);
      button.addChild(labelText);

      app.stage.addChild(button);
    });

    console.log('[PixiSlotMockup] Created default UI buttons:', defaultButtons.map(b => b.name));
  };

  // Create UI buttons from generated UI button sheet or individual buttons
  // const createUIButtons = async (app: PIXI.Application) => {
  //   // Remove existing UI buttons first (including control buttons)
  //   const existingButtons = app.stage.children.filter(child =>
  //     child.name && (child.name.startsWith('ui-button-') || child.name.startsWith('control-button-'))
  //   );
  //   existingButtons.forEach(button => {
  //     app.stage.removeChild(button);
  //     button.destroy();
  //   });

  //   // Check if UI buttons should be visible
  //   if (uiButtonAdjustments?.visibility === false) {
  //     console.log('[PixiSlotMockup] UI buttons hidden by visibility setting');
  //     return;
  //   }

  //   // Get UI buttons from config - check multiple possible locations
  //   const uiButtonsPath = (config as any)?.uiButtonsPath || (config as any)?.theme?.generated?.uiButtons;
  //   const extractedButtons = (config as any)?.extractedUIButtons || (config as any)?.uiElements;

  //   console.log('[PixiSlotMockup] UI Button Debug:', {
  //     uiButtonsPath: !!uiButtonsPath,
  //     extractedButtons: extractedButtons ? Object.keys(extractedButtons) : null,
  //     configKeys: Object.keys(config || {}),
  //     hasUiElements: !!(config as any)?.uiElements,
  //     hasExtractedUIButtons: !!(config as any)?.extractedUIButtons,
  //     uiButtonAdjustments: uiButtonAdjustments
  //   });

  //   // If no AI-generated buttons, show default buttons
  //   if (!uiButtonsPath && !extractedButtons) {
  //     console.log('[PixiSlotMockup] No AI buttons found, showing default buttons');
  //     createDefaultUIButtons(app);
  //     return;
  //   }

  //   try {
  //     // If we have extracted individual buttons, use those
  //     if (extractedButtons && Object.keys(extractedButtons).length > 0) {
  //       console.log('[PixiSlotMockup] Rendering individual UI buttons:', Object.keys(extractedButtons));

  //       // Map button names to match extraction function output
  //       const buttonMapping = [
  //         // { displayName: 'SPIN', extractedName: 'spinButton' },  
  //         // { displayName: 'AUTO', extractedName: 'autoplayButton' },
  //         // { displayName: 'MENU', extractedName: 'menuButton' },
  //         { displayName: 'SOUND', extractedName: 'soundButton' },
  //         { displayName: 'SETTINGS', extractedName: 'settingsButton' }
  //       ];

  //       const buttonSpacing = 90; // Reduced spacing for smaller buttons
  //       const startX = (containerDimensions.width - (buttonMapping.length * buttonSpacing)) / 2;
  //       const buttonY = containerDimensions.height - 80; // Position near bottom

  //       for (let i = 0; i < buttonMapping.length; i++) {
  //         const { displayName, extractedName } = buttonMapping[i];
  //         const buttonUrl = extractedButtons[extractedName] || extractedButtons[displayName] || extractedButtons[displayName.toLowerCase()];

  //         if (buttonUrl) {
  //           try {
  //             const buttonTexture = await loadTextureFromUrl(buttonUrl);
  //             if (buttonTexture) {
  //               const buttonSprite = new PIXI.Sprite(buttonTexture);

  //               // Set button name for identification and cleanup
  //               buttonSprite.name = `ui-button-${displayName.toLowerCase()}`;

  //               // Set base button size
  //               const baseButtonSize = displayName === 'SPIN' ? 80 : 60;

  //               // Apply scale first, then set size
  //               let finalScale = 1;
  //               if (uiButtonAdjustments?.scale && uiButtonAdjustments.scale !== 100) {
  //                 finalScale = uiButtonAdjustments.scale / 100;
  //               }

  //               // Set final button size with scale applied
  //               const finalButtonSize = baseButtonSize * finalScale;
  //               buttonSprite.width = finalButtonSize;
  //               buttonSprite.height = finalButtonSize;

  //               // Position button
  //               buttonSprite.x = startX + (i * buttonSpacing);
  //               buttonSprite.y = buttonY;

  //               // Apply UI button adjustments
  //               if (uiButtonAdjustments) {
  //                 const { position, scale } = uiButtonAdjustments;

  //                 // Apply position adjustments
  //                 if (position) {
  //                   buttonSprite.x += position.x;
  //                   buttonSprite.y += position.y;
  //                 }

  //                 console.log(`[PixiSlotMockup] ${displayName} button adjustments applied:`, {
  //                   baseSize: baseButtonSize,
  //                   scale: scale,
  //                   finalScale: finalScale,
  //                   finalSize: finalButtonSize,
  //                   position: position,
  //                   finalPosition: { x: buttonSprite.x, y: buttonSprite.y }
  //                 });
  //               }

  //               app.stage.addChild(buttonSprite);
  //               console.log(`[PixiSlotMockup] Added ${displayName} button at (${buttonSprite.x}, ${buttonSprite.y})`);
  //             }
  //           } catch (error) {
  //             console.error(`[PixiSlotMockup] Failed to load ${displayName} button:`, error);
  //           }
  //         }
  //       }
  //     }
  //     // Fallback to full button sheet if no extracted buttons
  //     else if (uiButtonsPath) {
  //       console.log('[PixiSlotMockup] Rendering UI button sheet:', uiButtonsPath);

  //       const buttonTexture = await loadTextureFromUrl(uiButtonsPath);
  //       if (buttonTexture) {
  //         const buttonSprite = new PIXI.Sprite(buttonTexture);

  //         // Set button name for identification and cleanup
  //         buttonSprite.name = 'ui-button-sheet';

  //         // Calculate base scale to fit in the UI area
  //         const maxWidth = containerDimensions.width * 0.8;
  //         const maxHeight = 100;
  //         const baseScale = Math.min(maxWidth / buttonTexture.width, maxHeight / buttonTexture.height);

  //         // Apply user scale adjustment
  //         let finalScale = baseScale;
  //         if (uiButtonAdjustments?.scale && uiButtonAdjustments.scale !== 100) {
  //           const userScale = uiButtonAdjustments.scale / 100;
  //           finalScale = baseScale * userScale;
  //         }

  //         // Set final button sheet size
  //         buttonSprite.width = buttonTexture.width * finalScale;
  //         buttonSprite.height = buttonTexture.height * finalScale;

  //         // Position at bottom center
  //         buttonSprite.x = (containerDimensions.width - buttonSprite.width) / 2;
  //         buttonSprite.y = containerDimensions.height - buttonSprite.height - 20;

  //         // Apply UI button adjustments
  //         if (uiButtonAdjustments) {
  //           const { position, scale: adjustScale } = uiButtonAdjustments;

  //           // Apply position adjustments
  //           if (position) {
  //             buttonSprite.x += position.x;
  //             buttonSprite.y += position.y;
  //           }

  //           console.log(`[PixiSlotMockup] Button sheet adjustments applied:`, {
  //             baseScale: baseScale,
  //             userScale: adjustScale,
  //             finalScale: finalScale,
  //             position: position,
  //             finalPosition: { x: buttonSprite.x, y: buttonSprite.y },
  //             finalSize: { width: buttonSprite.width, height: buttonSprite.height }
  //           });
  //         }

  //         app.stage.addChild(buttonSprite);
  //         console.log('[PixiSlotMockup] Added UI button sheet');
  //       }
  //     }
  //   } catch (error) {
  //     console.error('[PixiSlotMockup] Failed to create UI buttons:', error);
  //   }
  // };

  // Render the slot machine using PixiJS
  const renderSlotMachine = async (app: PIXI.Application) => {
    // Prevent multiple simultaneous renders with a more lenient approach
    if (isRendering) {
      console.log('[PixiSlotMockup] Already rendering, waiting briefly...');
      // Wait a short time and try again instead of completely skipping
      await new Promise(resolve => setTimeout(resolve, 100));
      if (isRendering) {
        console.log('[PixiSlotMockup] Still rendering after wait, skipping this render');
        return;
      }
    }

    setIsRendering(true);

    try {
      // Clear existing content
      app.stage.removeChildren();

      console.log('[PixiSlotMockup] Rendering with dimensions:', containerDimensions);

    const { width: containerWidth, height: containerHeight } = containerDimensions;

    // Calculate grid dimensions
    const gridDimensions = calculatePixiMockupDimensions({
      cols,
      rows,
      containerWidth,
      containerHeight,
      isMobile,
      orientation
    });

    // Create background
    if (finalBackground) {
      try {
        const backgroundTexture = await loadTextureFromUrl(finalBackground);
        if (backgroundTexture) {
          const backgroundSprite = new PIXI.Sprite(backgroundTexture);
          // Mark as background sprite for freespin transitions
          (backgroundSprite as any).isBackgroundSprite = true;

          // Apply background adjustments
          if (backgroundAdjustments) {
            const { position, scale, fit } = backgroundAdjustments;

            // Apply fit mode
            const fitMode = fit || 'cover';
            const textureAspect = backgroundTexture.width / backgroundTexture.height;
            const containerAspect = containerWidth / containerHeight;

            let spriteWidth = containerWidth;
            let spriteHeight = containerHeight;

            switch (fitMode) {
              case 'cover':
                if (textureAspect > containerAspect) {
                  spriteWidth = containerHeight * textureAspect;
                  spriteHeight = containerHeight;
                } else {
                  spriteWidth = containerWidth;
                  spriteHeight = containerWidth / textureAspect;
                }
                break;
              case 'contain':
                if (textureAspect > containerAspect) {
                  spriteWidth = containerWidth;
                  spriteHeight = containerWidth / textureAspect;
                } else {
                  spriteWidth = containerHeight * textureAspect;
                  spriteHeight = containerHeight;
                }
                break;
              case 'fill':
                spriteWidth = containerWidth;
                spriteHeight = containerHeight;
                break;
              case 'scale-down':
                const scaleDownWidth = Math.min(containerWidth, backgroundTexture.width);
                const scaleDownHeight = Math.min(containerHeight, backgroundTexture.height);
                if (textureAspect > containerAspect) {
                  spriteWidth = scaleDownWidth;
                  spriteHeight = scaleDownWidth / textureAspect;
                } else {
                  spriteWidth = scaleDownHeight * textureAspect;
                  spriteHeight = scaleDownHeight;
                }
                break;
            }

            // Apply scale adjustment
            const scaleValue = (scale || 100) / 100;
            spriteWidth *= scaleValue;
            spriteHeight *= scaleValue;

            backgroundSprite.width = spriteWidth;
            backgroundSprite.height = spriteHeight;

            // Center the background and apply position adjustments
            backgroundSprite.anchor.set(0.5);
            backgroundSprite.x = containerWidth / 2 + (position?.x || 0);
            backgroundSprite.y = containerHeight / 2 + (position?.y || 0);

            console.log('[PixiSlotMockup] Background applied with adjustments:', {
              position: backgroundAdjustments?.position,
              scale: backgroundAdjustments?.scale,
              fit: backgroundAdjustments?.fit,
              finalSize: { width: spriteWidth, height: spriteHeight },
              finalPosition: { x: backgroundSprite.x, y: backgroundSprite.y }
            });
          } else {
            // Default behavior without adjustments
            backgroundSprite.width = containerWidth;
            backgroundSprite.height = containerHeight;
          }

          app.stage.addChild(backgroundSprite);
        }
      } catch (error) {
        console.error('[PixiSlotMockup] Failed to load background:', error);
      }
    }

    // Create symbol grid
    await createSymbolGrid(app, gridDimensions);

    // Create frame overlay
    if (finalFrame) {
      try {
        const frameTexture = await loadTextureFromUrl(finalFrame);
        if (frameTexture) {
          const frameSprite = new PIXI.Sprite(frameTexture);
          frameSprite.name = 'frameOverlay'; // Add name for easier identification

          // Set initial size to container dimensions
          frameSprite.width = containerWidth;
          frameSprite.height = containerHeight;

          // Apply frame adjustments
          if (frameAdjustments) {
            const { position, scale, stretch } = frameAdjustments;

            // Set anchor first for proper positioning
            frameSprite.anchor.set(0.5);

            // Apply scale and stretch adjustments - use the exact values from adjustments
            const scaleValue = (scale || 100) / 100;
            const stretchX = (stretch?.x || 100) / 100;
            const stretchY = (stretch?.y || 100) / 100;

            frameSprite.scale.set(scaleValue * stretchX, scaleValue * stretchY);

            // Position the frame - center it and apply position adjustments
            frameSprite.x = containerWidth / 2 + (position?.x || 0);
            frameSprite.y = containerHeight / 2 + (position?.y || 0);
          } else {
            // Default positioning without adjustments
            frameSprite.anchor.set(0.5);
            frameSprite.x = containerWidth / 2;
            frameSprite.y = containerHeight / 2;
          }

          app.stage.addChild(frameSprite);

          console.log('[PixiSlotMockup] Frame applied with adjustments:', {
            frameUrl: finalFrame,
            position: frameAdjustments?.position,
            scale: frameAdjustments?.scale,
            stretch: frameAdjustments?.stretch,
            finalScale: frameSprite.scale,
            componentKey: `${cols}x${rows}-${isMobile ? 'mobile' : 'desktop'}-${orientation}`
          });
        }
      } catch (error) {
        console.error('[PixiSlotMockup] Failed to load frame:', error);
      }
    }

    // Create UI buttons
    // await createUIButtons(app);

    // Create programmatic reel dividers (ensures correct number)
    await createReelDividers(app, gridDimensions);
    
    // Ensure reel dividers are rendered on top of other elements but below UI
    const reelDividers = app.stage.children.filter(child => 
      child.name && child.name.startsWith('reel-divider')
    );
    reelDividers.forEach(divider => {
      app.stage.removeChild(divider);
      app.stage.addChild(divider); // Re-add to bring to front
    });

    // Create logo
    if (finalLogo) {
      await createLogo(app);
    }

    // Create UI controls
    if (showControls) {
      await createUIControls(app);
    }
    } catch (error) {
      console.error('[PixiSlotMockup] Error during rendering:', error);
    } finally {
      setIsRendering(false);
    }
  };

  // Create symbol grid
  const createSymbolGrid = async (app: PIXI.Application, gridDimensions: any) => {
    const { symbolSize, gridWidth, gridHeight, symbolPadding } = gridDimensions;

    // Generate display symbols
    const displaySymbols = generateDisplaySymbols();

    // Create grid container - CENTER IT PROPERLY like CSS version
    const gridContainer = new PIXI.Container();

    // EXACT MATCH TO CSS: Center the grid in the container
    // CSS uses: top: '40%', left: '50%', transform: 'translate(-50%, -50%)'
    // Symbol spacing remains fixed - only reel dividers are affected by gap setting
    gridContainer.x = containerDimensions.width * 0.5 - gridWidth * 0.5;
    gridContainer.y = containerDimensions.height * 0.4 - gridHeight * 0.5;

    // Apply custom offset for mobile views (same as CSS version)
    if (customOffset) {
      gridContainer.x += customOffset.x;
      gridContainer.y += customOffset.y;
    }

    // Apply grid adjustments from Step 6
    if (gridAdjustments) {
      const { position, scale, stretch } = gridAdjustments;

      // Apply position adjustments - use exact values from wrapper
      if (position) {
        gridContainer.x += position.x;
        gridContainer.y += position.y;
      }

      // Apply scale and stretch adjustments - use exact values from wrapper
      const scaleValue = (scale || 100) / 100;
      const stretchX = (stretch?.x || 100) / 100;
      const stretchY = (stretch?.y || 100) / 100;

      gridContainer.scale.set(scaleValue * stretchX, scaleValue * stretchY);
    }

    console.log('[PixiSlotMockup] Grid positioned at:', {
      x: gridContainer.x,
      y: gridContainer.y,
      scaleX: gridContainer.scale.x,
      scaleY: gridContainer.scale.y,
      gridWidth,
      gridHeight,
      containerWidth: containerDimensions.width,
      containerHeight: containerDimensions.height,
      customOffset,
      gridAdjustments,
      isVisible: gridContainer.visible,
      alpha: gridContainer.alpha,
      bounds: {
        left: gridContainer.x,
        right: gridContainer.x + (gridWidth * gridContainer.scale.x),
        top: gridContainer.y,
        bottom: gridContainer.y + (gridHeight * gridContainer.scale.y)
      }
    });

    // Create reel backgrounds first (behind symbols)
    await createReelBackgrounds(gridContainer, gridDimensions);

    // Clear existing reel containers for animation
    reelContainersRef.current = [];
    console.log('[PixiSlotMockup] Cleared reel containers, creating new ones for', cols, 'columns');

    // Create reel containers for animation support
    for (let col = 0; col < cols; col++) {
      const reelContainer = new PIXI.Container();
      // Symbol spacing remains fixed - only reel dividers are affected by gap setting
      reelContainer.x = col * (symbolSize + symbolPadding);

      // Create symbols for this reel
      for (let row = 0; row < rows; row++) {
        const index = row * cols + col;
        const symbol = displaySymbols[index];

        const cellY = row * (symbolSize + symbolPadding);

        // Create cell background
        const cellTexture = createSymbolCellTexture(app, symbolSize);
        const cellSprite = new PIXI.Sprite(cellTexture);
        cellSprite.x = 0;
        cellSprite.y = cellY;
        reelContainer.addChild(cellSprite);

        // Create symbol (always create for animation, but control visibility)
        if (symbol) {
          try {
            const symbolTexture = await loadTextureFromUrl(symbol);
            if (symbolTexture) {
              const symbolSprite = new PIXI.Sprite(symbolTexture);
              symbolSprite.width = symbolSize * 0.8;
              symbolSprite.height = symbolSize * 0.8;
              symbolSprite.x = (symbolSize - symbolSprite.width) * 0.5;
              symbolSprite.y = cellY + (symbolSize - symbolSprite.height) * 0.5;

              // Mark as symbol sprite for easy identification during animation
              (symbolSprite as any).isSymbolSprite = true;

              // Control visibility based on hideSymbols prop and spinning state
              symbolSprite.visible = !hideSymbols;

              reelContainer.addChild(symbolSprite);
            }
          } catch (error) {
            console.error(`[PixiSlotMockup] Failed to load symbol ${symbol}:`, error);
          }
        }
      }

      // Add reel container to grid and store reference for animation
      gridContainer.addChild(reelContainer);
      reelContainersRef.current.push(reelContainer);
    }

    console.log('[PixiSlotMockup] Created', reelContainersRef.current.length, 'reel containers for animation');

    // Ensure grid container is visible and properly configured
    gridContainer.visible = true;
    gridContainer.alpha = 1;

    // Add safety check for grid container bounds
    const gridBounds = {
      left: gridContainer.x,
      right: gridContainer.x + (gridWidth * gridContainer.scale.x),
      top: gridContainer.y,
      bottom: gridContainer.y + (gridHeight * gridContainer.scale.y)
    };

    console.log('[PixiSlotMockup] Grid container bounds check:', {
      bounds: gridBounds,
      containerSize: { width: containerDimensions.width, height: containerDimensions.height },
      isWithinContainer: {
        horizontally: gridBounds.right > 0 && gridBounds.left < containerDimensions.width,
        vertically: gridBounds.bottom > 0 && gridBounds.top < containerDimensions.height
      },
      scale: { x: gridContainer.scale.x, y: gridContainer.scale.y },
      visible: gridContainer.visible,
      alpha: gridContainer.alpha
    });

    app.stage.addChild(gridContainer);
  };

  // Create reel backgrounds with click handlers
  const createReelBackgrounds = async (gridContainer: PIXI.Container, gridDimensions: any) => {
    const { symbolSize, symbolPadding } = gridDimensions;

    // Calculate reel dimensions
    const reelWidth = symbolSize + symbolPadding;
    const reelHeight = rows * (symbolSize + symbolPadding);

    console.log('[PixiSlotMockup] Creating reel backgrounds:', {
      cols,
      reelBackgrounds: reelBackgrounds.length,
      reelBackgroundsData: reelBackgrounds,
      reelWidth,
      reelHeight
    });

    for (let col = 0; col < cols; col++) {
      // Reel background spacing remains fixed - only dividers are affected by gap setting
      const reelX = col * reelWidth;
      const reelY = 0;

      // Create reel container for this column
      const reelContainer = new PIXI.Container();
      reelContainer.x = reelX;
      reelContainer.y = reelY;
      reelContainer.name = `reel-${col}`;

      // Create reel background
      if (reelBackgrounds[col]) {
        try {
          // Load the generated reel background
          const reelTexture = await loadTextureFromUrl(reelBackgrounds[col]);
          if (reelTexture) {
            // Create tiling sprite for seamless vertical repetition
            const tilingSprite = new PIXI.TilingSprite(reelTexture, reelWidth, reelHeight);
            tilingSprite.tileScale.set(reelWidth / reelTexture.width, reelHeight / reelTexture.height);

            reelContainer.addChild(tilingSprite);
            console.log(`[PixiSlotMockup] Added reel background for reel ${col + 1}:`, reelBackgrounds[col]);
          }
        } catch (error) {
          console.error(`[PixiSlotMockup] Failed to load reel background for reel ${col}:`, error);
        }
      } else {
        // Add placeholder background to show clickable area
        const placeholderBg = new PIXI.Graphics();
        placeholderBg.beginFill(0x333333, 0.1); // Very subtle dark background
        placeholderBg.drawRect(0, 0, reelWidth, reelHeight);
        placeholderBg.endFill();

        // Add dashed border to indicate clickable area
        // placeholderBg.lineStyle(1, 0x666666, 0.3, 0.5);
        placeholderBg.drawRect(2, 2, reelWidth - 4, reelHeight - 4);

        reelContainer.addChild(placeholderBg);
        console.log(`[PixiSlotMockup] Added placeholder for reel ${col + 1} (clickable)`);
      }

      // Create invisible click area for reel generation
      const clickArea = new PIXI.Graphics();
      clickArea.beginFill(0x000000, 0); // Transparent
      clickArea.drawRect(0, 0, reelWidth, reelHeight);
      clickArea.endFill();
      clickArea.interactive = true;
      clickArea.cursor = 'pointer';
      clickArea.name = `reel-click-${col}`;

      // Add click handler
      clickArea.on('pointerdown', () => {
        console.log(`[PixiSlotMockup] Clicked on reel ${col + 1}`);
        generateReelBackground(col);
      });

      // Add hover effects
      clickArea.on('pointerover', () => {
        clickArea.clear();
        clickArea.beginFill(0x00FF00, 0.2); // Green highlight on hover
        clickArea.drawRect(0, 0, reelWidth, reelHeight);
        clickArea.endFill();

        // Add text hint
        const hintText = new PIXI.Text('Click to generate\nreel background', {
          fontSize: 10,
          fill: 0xFFFFFF,
          align: 'center',
          stroke: 0x000000,
          strokeThickness: 2
        });
        hintText.x = reelWidth / 2 - hintText.width / 2;
        hintText.y = reelHeight / 2 - hintText.height / 2;
        hintText.name = 'hover-hint';

        clickArea.addChild(hintText);
      });

      clickArea.on('pointerout', () => {
        clickArea.clear();
        clickArea.beginFill(0x000000, 0); // Back to transparent
        clickArea.drawRect(0, 0, reelWidth, reelHeight);
        clickArea.endFill();

        // Remove hint text
        const hintText = clickArea.getChildByName('hover-hint');
        if (hintText) {
          clickArea.removeChild(hintText);
        }
      });

      reelContainer.addChild(clickArea);

      // Add loading indicator if this reel is being generated
      if (isGeneratingReel === col) {
        const loadingBg = new PIXI.Graphics();
        loadingBg.beginFill(0x000000, 0.7);
        loadingBg.drawRect(0, 0, reelWidth, reelHeight);
        loadingBg.endFill();

        // Simple loading text (you could make this more fancy)
        const loadingText = new PIXI.Text('Generating...', {
          fontSize: 12,
          fill: 0xFFFFFF,
          align: 'center'
        });
        loadingText.x = reelWidth / 2 - loadingText.width / 2;
        loadingText.y = reelHeight / 2 - loadingText.height / 2;

        loadingBg.addChild(loadingText);
        reelContainer.addChild(loadingBg);

        console.log(`[PixiSlotMockup] Added loading indicator for reel ${col + 1}`);
      }

      gridContainer.addChild(reelContainer);
    }
  };

  // Generate display symbols with proper spin result integration
  const generateDisplaySymbols = () => {
    const totalCells = cols * rows;
    const displaySymbols: string[] = [];

    // Check if we have a current spin result to display
    if (currentSpinResultRef.current && currentSpinResultRef.current.length > 0) {
      console.log('[PixiSlotMockup] Using current spin result for display:', currentSpinResultRef.current);
      
      // Convert from reel-based (column) to grid-based (row) layout
      for (let row = 0; row < rows; row++) {
        for (let col = 0; col < cols; col++) {
          if (currentSpinResultRef.current[col] && currentSpinResultRef.current[col][row]) {
            displaySymbols.push(currentSpinResultRef.current[col][row]);
          } else {
            // Fallback to random symbol if spin result is incomplete
            const fallbackSymbol = finalSymbols[Math.floor(Math.random() * finalSymbols.length)];
            const symbolUrl = typeof fallbackSymbol === 'string' ? fallbackSymbol : (fallbackSymbol as any)?.url || (fallbackSymbol as any)?.imageUrl;
            displaySymbols.push(symbolUrl);
          }
        }
      }
      
      console.log('[PixiSlotMockup] Generated display symbols from spin result:', displaySymbols.length);
      return displaySymbols;
    }

    // Use final spin symbols if available (after animation), otherwise use original symbols
    let symbolsToUse = finalSymbols;

    if (finalSpinSymbols.length > 0) {
      // Convert from column-based (reel-based) to row-based (grid-based) layout
      const gridSymbols: string[] = [];
      
      for (let row = 0; row < rows; row++) {
        for (let col = 0; col < cols; col++) {
          if (finalSpinSymbols[col] && finalSpinSymbols[col][row]) {
            gridSymbols.push(finalSpinSymbols[col][row]);
          } else {
            // Fallback to original symbols if spin result is missing
            const fallbackIndex = row * cols + col;
            const fallbackSymbol = finalSymbols[fallbackIndex % finalSymbols.length];
            const symbolUrl = typeof fallbackSymbol === 'string' ? fallbackSymbol : (fallbackSymbol as any)?.url || (fallbackSymbol as any)?.imageUrl;
            gridSymbols.push(symbolUrl);
          }
        }
      }
      symbolsToUse = gridSymbols;
    }

    if (symbolsToUse.length === 0) {
      return generatePixiPlaceholderSymbols(cols, rows, 'default');
    } else {
      for (let i = 0; i < totalCells; i++) {
        const symbol = symbolsToUse[i % symbolsToUse.length];
        const symbolUrl = typeof symbol === 'string' ? symbol : (symbol as any)?.url || (symbol as any)?.imageUrl;
        displaySymbols.push(symbolUrl);
      }
    }

    return displaySymbols;
  };

  // Create programmatic reel dividers or AI reel images
  const createReelDividers = async (app: PIXI.Application, gridDimensions: any) => {
    console.log(`[PixiSlotMockup] createReelDividers called - frameStyle: ${(config as any)?.frameStyle}, aiReelImage: ${!!(config as any)?.aiReelImage}, hasFrame: ${!!finalFrame}`);

    // Remove existing reel dividers with detailed logging
    const existingDividers = app.stage.children.filter(child =>
      child.name && child.name.startsWith('reel-divider')
    );

    if (existingDividers.length > 0) {
      console.log(`[PixiSlotMockup] Removing ${existingDividers.length} existing reel dividers:`, existingDividers.map(d => d.name));
      existingDividers.forEach(divider => {
        app.stage.removeChild(divider);
        divider.destroy();
      });
    }

    // Get the latest AI reel image from config (this ensures we get updates)
    const currentConfig = useGameStore.getState().config;
    const aiReelImage = currentConfig?.aiReelImage || (config as any)?.aiReelImage;
    const frameStyle = currentConfig?.frameStyle || (config as any)?.frameStyle;

    console.log(`[PixiSlotMockup] Using latest config - frameStyle: ${frameStyle}, aiReelImage: ${!!aiReelImage}`);

    // Only create dividers if frame style includes reels
    if (frameStyle !== 'reel' && frameStyle !== 'both') {
      console.log(`[PixiSlotMockup] Frame style '${frameStyle}' - no reel dividers needed`);
      return;
    }

    // Check if we have a frame that might already contain reel dividers
    const hasFrame = !!finalFrame;

    // Only skip for 'both' frame style if there's no AI reel image (meaning we want frame-only dividers)
    if (hasFrame && frameStyle === 'both' && !aiReelImage) {
      console.log(`[PixiSlotMockup] Frame style 'both' with existing frame and no AI reel image - using frame's built-in dividers`);
      return;
    }

    // Use the same grid positioning logic as the symbol grid
    const { symbolSize, gridWidth, gridHeight, symbolPadding } = gridDimensions;
    
    // Match the exact grid container positioning from createSymbolGrid
    let baseGridX = containerDimensions.width * 0.5 - gridWidth * 0.5;
    let baseGridY = containerDimensions.height * 0.4 - gridHeight * 0.5;
    
    // Apply grid adjustments to match symbol grid positioning
    if (gridAdjustments?.position) {
      baseGridX += gridAdjustments.position.x;
      baseGridY += gridAdjustments.position.y;
    }
    
    const gridScale = (gridAdjustments?.scale || 100) / 100;
    const stretchX = (gridAdjustments?.stretch?.x || 100) / 100;
    const stretchY = (gridAdjustments?.stretch?.y || 100) / 100;
    
    const adjustedGridX = baseGridX;
    const adjustedGridY = baseGridY;
    const finalGridWidth = gridWidth * gridScale * stretchX;
    const finalGridHeight = gridHeight * gridScale * stretchY;

    // Create exactly (cols - 1) vertical dividers
    const numDividers = cols - 1;

    if ((frameStyle === 'reel' || frameStyle === 'both') && aiReelImage) {
      // Create AI reel dividers when we have an AI reel image
      console.log(`[PixiSlotMockup] Creating ${numDividers} adjustable AI reel dividers for ${cols} columns using image:`, aiReelImage);

      try {
        const reelTexture = await loadTextureFromUrl(aiReelImage);
        if (reelTexture) {
          // Get the most up-to-date reel divider settings from the store
          const latestReelGap = currentConfig?.reelGap || reelGapSettings.gap || 1;
          const latestReelPosition = currentConfig?.reelDividerPosition || reelGapSettings.position || { x: 0, y: 0 };
          const latestReelStretch = currentConfig?.reelDividerStretch || reelGapSettings.stretch || { x: 100, y: 100 };

          console.log('[PixiSlotMockup] Using latest reel divider settings:', {
            gap: latestReelGap,
            position: latestReelPosition,
            stretch: latestReelStretch
          });

          // Calculate reel column positions (symbols maintain fixed spacing)
          const { symbolSize, symbolPadding } = gridDimensions;
          const reelColumnSpacing = symbolSize + symbolPadding;

          for (let i = 1; i <= numDividers; i++) {
            const reelSprite = new PIXI.Sprite(reelTexture);
            reelSprite.name = `reel-divider-${i}`;

            // Calculate base dimensions - use fixed width for dividers
            const baseWidth = 20; // Fixed divider width
            const baseHeight = finalGridHeight;

            // Apply stretch settings
            reelSprite.width = baseWidth * (latestReelStretch.x / 100);
            reelSprite.height = baseHeight * (latestReelStretch.y / 100);

            // Position dividers between reels with gap control
            let baseX;
            
            // Position dividers between reel columns using the same spacing as symbols
            const reelColumnSpacing = symbolSize + symbolPadding;
            
            // Calculate position between reel columns
            const reel1X = (i - 1) * reelColumnSpacing;
            const reel2X = i * reelColumnSpacing;
            const naturalCenterX = (reel1X + reel2X) / 2;
            
            // Apply gap adjustment
            const gapMultiplier = latestReelGap / 100;
            const spreadFactor = Math.max(0.5, gapMultiplier);
            
            const gridCenterX = finalGridWidth / 2;
            const offsetFromCenter = (naturalCenterX - gridCenterX) * spreadFactor;
            baseX = adjustedGridX + gridCenterX + offsetFromCenter - (reelSprite.width / 2);

            const baseY = adjustedGridY;
            
            // Apply grid scaling to the divider position
            reelSprite.x = (baseX + (latestReelPosition?.x || 0)) * gridScale * stretchX;
            reelSprite.y = (baseY + (latestReelPosition?.y || 0)) * gridScale * stretchY;
            
            // Apply the same scaling as the grid
            reelSprite.width *= gridScale * stretchX;
            reelSprite.height *= gridScale * stretchY;



            // Ensure reel dividers are visible
            reelSprite.visible = true;
            reelSprite.alpha = 1;

            app.stage.addChild(reelSprite);
            console.log(`[PixiSlotMockup] Added reel divider ${i} at position (${reelSprite.x}, ${reelSprite.y})`);
          }

          // Verify the final count
          const finalDividerCount = app.stage.children.filter(child =>
            child.name && child.name.startsWith('reel-divider')
          ).length;
          console.log(`[PixiSlotMockup] Successfully created ${numDividers} AI reel dividers. Total dividers on stage: ${finalDividerCount}`);
          return;
        }
      } catch (error) {
        console.error('[PixiSlotMockup] Failed to load AI reel image:', error);
      }
    }

    // For 'reel' frame style without AI image, create no dividers
    if (frameStyle === 'reel' && !aiReelImage) {
      console.log('[PixiSlotMockup] Reel frame style selected but no AI reel image available - no dividers created');
      return;
    }

    // For other frame styles, no dividers needed
    console.log(`[PixiSlotMockup] Frame style '${frameStyle}' - no additional reel dividers needed`);
  };

  // Create logo
  const createLogo = async (app: PIXI.Application) => {
    if (!finalLogo) return;

    try {
      const logoTexture = await loadTextureFromUrl(finalLogo);
      if (logoTexture) {
        const logoSprite = new PIXI.Sprite(logoTexture);
        logoSprite.name = 'logo'; // Add name for easier identification

        // Get the most up-to-date logo position from the store
        // This ensures we use the latest position even after tab switches
        const currentConfig = useGameStore.getState().config;
        const currentDevice = detectDeviceType();
        const latestLogoPosition = currentConfig?.logoPositions?.[currentDevice] || logoPosition;
        const latestLogoScale = currentConfig?.logoScales?.[currentDevice] || logoScale;

        console.log('[PixiSlotMockup] Rendering logo with latest position:', {
          propPosition: logoPosition,
          latestPosition: latestLogoPosition,
          propScale: logoScale,
          latestScale: latestLogoScale,
          currentDevice
        });

        // Position logo using same logic as CSS version
        const baseX = containerDimensions.width * 0.5;
        const baseY = containerDimensions.height * 0.1;

        logoSprite.anchor.set(0.5, 0.5);
        logoSprite.x = baseX + latestLogoPosition.x;
        logoSprite.y = baseY + latestLogoPosition.y;
        logoSprite.scale.set(latestLogoScale / 100 * 0.15); // Small base scale since we're using full 1024px image

        // Add interactive behavior if in positioning mode
        if (logoPositioningMode) {
          logoSprite.interactive = true;
          logoSprite.cursor = 'pointer';

          logoSprite.on('pointerdown', (event: PIXI.FederatedPointerEvent) => {
            setIsDraggingLogo(true);
            const globalPos = event.global;
            setDragStart({ x: globalPos.x, y: globalPos.y });
            setStartPosition({ x: latestLogoPosition.x, y: latestLogoPosition.y });
          });
        }

        app.stage.addChild(logoSprite);
      }
    } catch (error) {
      console.error('[PixiSlotMockup] Failed to load logo:', error);
    }
  };

  // Create UI controls with responsive positioning
  const createUIControls = async (app: PIXI.Application) => {
    const { width: containerWidth, height: containerHeight } = containerDimensions;

    // Remove existing UI container if it exists
    const existingUIContainer = app.stage.children.find(child => child.name === 'uiContainer');
    if (existingUIContainer) {
      app.stage.removeChild(existingUIContainer);
      existingUIContainer.destroy();
    }

    // Create UI container for better management
    const uiContainer = new PIXI.Container();
    uiContainer.name = 'uiContainer';

    // Adjust UI positioning based on view mode and mobile status
    const uiBottomOffset = isMobile ?
      (orientation === 'portrait' ? 80 : 60) :
      71; // Desktop offset

    const brandingHeight = 16;

    // Bottom UI Bar - positioned relative to container bottom
    const uiBar = new PIXI.Graphics();
    uiBar.beginFill(pixiMockupStyles.uiBackgroundColor, pixiMockupStyles.uiBackgroundAlpha);
    uiBar.drawRect(0, containerHeight - uiBottomOffset, containerWidth, 55);
    uiBar.endFill();
    uiContainer.addChild(uiBar);

    // Create UI text elements with responsive positioning
    const textY = containerHeight - uiBottomOffset + 27; // Center in UI bar
    createUIText(uiContainer, 'BET', '1.00', 60, textY, 'left');
    createUIText(uiContainer, 'WIN', '0.00', containerWidth - 180, textY, 'right', pixiMockupStyles.accentColor);
    createUIText(uiContainer, 'BALANCE', '1,000.00', containerWidth - 120, textY, 'right');

    // Create all UI buttons - matching CSS layout
    await createAllUIButtons(uiContainer, containerWidth, textY);

    // Bottom branding strip
    const brandingBar = new PIXI.Graphics();
    brandingBar.beginFill(0x111827, 1);
    brandingBar.drawRect(0, containerHeight - brandingHeight, containerWidth, brandingHeight);
    brandingBar.endFill();
    uiContainer.addChild(brandingBar);

    // Branding text
    const brandingText = new PIXI.Text('Premium Game | Game Crafter', {
      fontFamily: 'Arial',
      fontSize: 7,
      fill: 0xffffff,
      fontWeight: 'bold'
    });
    brandingText.x = 20;
    brandingText.y = containerHeight - brandingHeight + 5; // Center in branding bar
    uiContainer.addChild(brandingText);

    // Add UI container to stage
    app.stage.addChild(uiContainer);

    console.log('[PixiSlotMockup] UI positioned:', {
      containerWidth,
      containerHeight,
      uiBottomOffset,
      textY,
      isMobile,
      orientation
    });
  };

  // Create UI text helper
  const createUIText = (
    container: PIXI.Container,
    label: string,
    value: string,
    x: number,
    y: number,
    align: 'left' | 'right' = 'left',
    valueColor: number = pixiMockupStyles.textColor
  ) => {
    const labelText = new PIXI.Text(label, {
      fontFamily: 'Arial',
      fontSize: 8,
      fill: 0x6b7280,
      fontWeight: 'bold'
    });

    const valueText = new PIXI.Text(value, {
      fontFamily: 'Arial',
      fontSize: 12,
      fill: valueColor,
      fontWeight: 'bold'
    });

    if (align === 'right') {
      labelText.anchor.set(1, 0);
      valueText.anchor.set(1, 0);
    }

    labelText.x = x;
    labelText.y = y - 15;
    valueText.x = x;
    valueText.y = y - 5;

    container.addChild(labelText);
    container.addChild(valueText);
  };

  // Create all UI buttons - matching CSS layout with AI replacement support
  const createAllUIButtons = async (container: PIXI.Container, containerWidth: number, textY: number) => {
    // Remove existing control buttons first
    const existingControlButtons = container.children.filter(child =>
      child.name && child.name.startsWith('control-button-')
    );
    existingControlButtons.forEach(button => {
      container.removeChild(button);
      button.destroy();
    });

    // Get AI-generated buttons
    const extractedButtons = (config as any)?.extractedUIButtons || (config as any)?.uiElements;

    // Button definitions matching CSS layout
    const buttonDefinitions = [
      {
        name: 'AUTO',
        aiKey: 'autoplayButton',
        x: containerWidth / 2 - 60,
        size: 28,
        color: 0x1F2937,
        icon: '⟲'
      },
      {
        name: 'SPIN',
        aiKey: 'spinButton',
        x: containerWidth / 2,
        size: 40,
        color: 0x10B981,
        icon: '▶',
        isMain: true
      },
      {
        name: 'QUICK',
        aiKey: 'quickButton',
        x: containerWidth / 2 + 60,
        size: 28,
        color: 0x1F2937,
        icon: '⏩'
      },
      {
        name: 'SOUND',
        aiKey: 'soundButton',
        x: containerWidth - 80,
        size: 32,
        color: 0x1F2937,
        icon: '♪'
      },
      {
        name: 'SETTINGS',
        aiKey: 'settingsButton',
        x: containerWidth - 40,
        size: 32,
        color: 0x1F2937,
        icon: '⚙'
      }
    ];

    // Create each button
    for (const buttonDef of buttonDefinitions) {
      await createControlButton(container, buttonDef, textY, extractedButtons);
    }

    console.log('[PixiSlotMockup] Created all UI control buttons');
  };

  // Create individual control button with AI replacement support
  const createControlButton = async (
    container: PIXI.Container,
    buttonDef: any,
    y: number,
    extractedButtons: any
  ) => {
    const aiButtonUrl = extractedButtons?.[buttonDef.aiKey] || extractedButtons?.[buttonDef.name];

    // Apply UI button adjustments
    const adjustedScale = (uiButtonAdjustments.scale || 100) / 100;
    const adjustedX = buttonDef.x + (uiButtonAdjustments.position?.x || 0);
    const adjustedY = y + (uiButtonAdjustments.position?.y || 0);
    const adjustedSize = buttonDef.size * adjustedScale;

    if (aiButtonUrl) {
      try {
        // Use AI-generated button
        const buttonTexture = await loadTextureFromUrl(aiButtonUrl);
        if (buttonTexture) {
          const buttonSprite = new PIXI.Sprite(buttonTexture);
          buttonSprite.name = `control-button-${buttonDef.name.toLowerCase()}`;

          // Set size with scale adjustment
          buttonSprite.width = adjustedSize;
          buttonSprite.height = adjustedSize;

          // Center and position with adjustments
          buttonSprite.anchor.set(0.5);
          buttonSprite.x = adjustedX;
          buttonSprite.y = adjustedY;
          buttonSprite.interactive = true;
          buttonSprite.cursor = 'pointer';

          // Apply visibility
          buttonSprite.visible = uiButtonAdjustments.visibility !== false;

          container.addChild(buttonSprite);
          console.log(`[PixiSlotMockup] Using AI-generated ${buttonDef.name} button with adjustments:`, {
            scale: adjustedScale,
            position: { x: adjustedX, y: adjustedY },
            visible: buttonSprite.visible
          });
          return;
        }
      } catch (error) {
        console.error(`[PixiSlotMockup] Failed to load AI ${buttonDef.name} button, using default:`, error);
      }
    }

    // Fallback to default graphics button
    const button = new PIXI.Graphics();
    button.name = `control-button-${buttonDef.name.toLowerCase()}`;

    // Create button background with adjusted size
    button.beginFill(buttonDef.color, buttonDef.isMain ? 1 : 0.8);
    button.drawCircle(0, 0, adjustedSize / 2);
    button.endFill();

    // Add border for main button
    if (buttonDef.isMain) {
      button.lineStyle(2, 0xFFFFFF, 0.2);
      button.drawCircle(0, 0, adjustedSize / 2);
    }

    // Position with adjustments
    button.x = adjustedX;
    button.y = adjustedY;
    button.interactive = true;
    button.cursor = 'pointer';

    // Apply visibility
    button.visible = uiButtonAdjustments.visibility !== false;

    // Add icon with adjusted size
    const iconText = new PIXI.Text(buttonDef.icon, {
      fontFamily: 'Arial',
      fontSize: adjustedSize / 3,
      fill: 0xFFFFFF,
      fontWeight: 'bold'
    });
    iconText.anchor.set(0.5);
    button.addChild(iconText);

    container.addChild(button);
    console.log(`[PixiSlotMockup] Using default ${buttonDef.name} button with adjustments:`, {
      scale: adjustedScale,
      position: { x: adjustedX, y: adjustedY },
      visible: button.visible
    });
  };



  return (
    <div 
      ref={containerRef}
      className={`pixi-slot-mockup ${className}`}
      style={{
        position: 'absolute',
        top: 0,
        left: 0,
        width: '100%',
        height: '100%',
        overflow: 'hidden'
      }}
    />
  );
};

export default PixiSlotMockup;
