import React, { useState, useEffect } from 'react';
import { slotApiClient } from '../utils/apiClient';
import { Calendar, GamepadIcon, Clock } from 'lucide-react';

interface GameConfig {
  gameId: string;
  displayName: string;
  gameType: string;
  theme: any;
  created_at: string;
  updated_at: string;
}

interface SavedConfigsSectionProps {
  onConfigSelect?: (config: GameConfig) => void;
}

const SavedConfigsSection: React.FC<SavedConfigsSectionProps> = ({ onConfigSelect }) => {
  const [configs, setConfigs] = useState<GameConfig[]>([]);
  const [loading, setLoading] = useState(true);
  const [metadata, setMetadata] = useState<any>(null);

  useEffect(() => {
    fetchConfigs();
  }, []);

  const fetchConfigs = async () => {
    setLoading(true);
    try {
      const result = await slotApiClient.fetchGameConfigs(1, 50);
      setConfigs(result.configs);
      setMetadata(result.metadata);
    } catch (error) {
      console.error('Error fetching configs:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString || dateString === '0001-01-01T00:00:00Z') {
      return 'Not set';
    }
    return new Date(dateString).toLocaleDateString();
  };

  const getGameTypeDisplay = (gameType: string) => {
    if (!gameType) return 'Not set';
    return gameType.charAt(0).toUpperCase() + gameType.slice(1).replace('-', ' ');
  };

  if (loading) {
    return (
      <section className="mb-8">
        <h2 className="text-2xl font-bold mb-6 text-gray-800">Saved Configurations</h2>
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
          <span className="ml-3 text-gray-600">Loading configurations...</span>
        </div>
      </section>
    );
  }

  if (configs.length === 0) {
    return (
      <section className="mb-8">
        <h2 className="text-2xl font-bold mb-6 text-gray-800">Saved Configurations</h2>
        <div className="text-center py-12 bg-gray-50 rounded-lg">
          <GamepadIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <p className="text-gray-600">No saved configurations found</p>
        </div>
      </section>
    );
  }

  return (
    <section className="mb-8">
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-gray-800">Saved Configurations</h2>
        {metadata && (
          <span className="text-sm text-gray-500">
            {metadata.totalRecords} configuration{metadata.totalRecords !== 1 ? 's' : ''}
          </span>
        )}
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {configs.map((config) => (
          <div
            key={config.gameId}
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow cursor-pointer"
            onClick={() => onConfigSelect?.(config)}
          >
            <div className="flex items-start justify-between mb-3">
              <h3 className="font-semibold text-gray-800 truncate flex-1">
                {config.gameId}
              </h3>
              <GamepadIcon className="h-5 w-5 text-gray-400 ml-2 flex-shrink-0" />
            </div>
            
            <div className="space-y-2 text-sm text-gray-600">
              <div className="flex items-center">
                <span className="font-medium w-20">Type:</span>
                <span>{getGameTypeDisplay(config.gameType)}</span>
              </div>
              
              <div className="flex items-center">
                <Calendar className="h-4 w-4 mr-1" />
                <span className="font-medium w-16">Created:</span>
                <span>{formatDate(config.created_at)}</span>
              </div>
              
              <div className="flex items-center">
                <Clock className="h-4 w-4 mr-1" />
                <span className="font-medium w-16">Updated:</span>
                <span>{formatDate(config.updated_at)}</span>
              </div>
            </div>
            
            <div className="mt-4 pt-3 border-t border-gray-100">
              <button className="w-full bg-red-600 hover:bg-red-700 text-white text-sm py-2 px-3 rounded-md transition-colors">
                Load Configuration
              </button>
            </div>
          </div>
        ))}
      </div>
    </section>
  );
};

export default SavedConfigsSection;